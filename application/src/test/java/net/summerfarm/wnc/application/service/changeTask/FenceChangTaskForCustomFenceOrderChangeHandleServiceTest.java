package net.summerfarm.wnc.application.service.changeTask;

import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * 自定义围栏切仓订单处理服务测试
 */
@ExtendWith(MockitoExtension.class)
class FenceChangTaskForCustomFenceOrderChangeHandleServiceTest {

    @Mock
    private OfcQueryFacade ofcQueryFacade;
    @Mock
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Mock
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Mock
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Mock
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    @InjectMocks
    private FenceChangTaskForCustomFenceOrderChangeHandleService service;

    private FenceChangeTaskEntity testTask;

    @BeforeEach
    void setUp() {
        testTask = new FenceChangeTaskEntity();
        testTask.setId(1L);
        testTask.setExeTimePlus2Date(LocalDateTime.now().plusDays(2));
    }

    @Test
    void testCustomFenceChangeOrderHandle_NullTask() {
        // 测试空任务
        service.customFenceChangeOrderHandle(null);
        
        verify(wncFenceChangeRecordsQueryRepository, never()).selectWithAreaByFenceChangeId(anyLong());
    }

    @Test
    void testCustomFenceChangeOrderHandle_NoAreaChange() {
        // 模拟无区域变更的情况
        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(1L))
                .thenReturn(Collections.emptyList());

        service.customFenceChangeOrderHandle(testTask);

        verify(fenceChangeTaskRepository).update(any(FenceChangeTaskEntity.class));
    }

    @Test
    void testCustomFenceChangeOrderHandle_WithAreaChange() {
        // 准备测试数据
        WncFenceChangeRecordsEntity beforeRecord = new WncFenceChangeRecordsEntity();
        beforeRecord.setId(1L);
        beforeRecord.setFenceId(100);
        beforeRecord.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue());
        beforeRecord.setFenceStoreNo(1001);
        beforeRecord.setFenceAreaNo(2001);

        WncFenceAreaChangeRecordsEntity areaRecord = new WncFenceAreaChangeRecordsEntity();
        areaRecord.setId(1L);
        areaRecord.setFenceId(100);
        areaRecord.setCity("北京市");
        areaRecord.setArea("朝阳区");
        areaRecord.setCustomAreaName("自定义朝阳区");
        beforeRecord.setAreaChangeRecords(Arrays.asList(areaRecord));

        WncFenceChangeRecordsEntity afterRecord = new WncFenceChangeRecordsEntity();
        afterRecord.setId(2L);
        afterRecord.setFenceId(100);
        afterRecord.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
        afterRecord.setFenceStoreNo(1002); // 城配仓变更
        afterRecord.setFenceAreaNo(2002); // 运营区域变更

        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(1L))
                .thenReturn(Arrays.asList(beforeRecord, afterRecord));

        // 模拟OFC查询返回订单
        FulfillmentOrderDTO order = new FulfillmentOrderDTO();
        order.setOuterOrderId("TEST001");
        order.setCity("北京市");
        order.setArea("自定义朝阳区");
        order.setPoi("116.123,39.456");
        when(ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(any(FulfillmentQueryInput.class)))
                .thenReturn(Arrays.asList(order));

        // 模拟保存订单成功
        when(fenceChangeTaskDetailRepository.saveBatch(any())).thenReturn(true);

        service.customFenceChangeOrderHandle(testTask);

        verify(ofcQueryFacade).queryTimePlus2WaitFulfillmentOrder(any(FulfillmentQueryInput.class));
        verify(fenceChangeTaskDetailRepository).saveBatch(any());
        verify(fenceChangeTaskRepository).update(any(FenceChangeTaskEntity.class));
    }

    @Test
    void testCustomFenceChangeOrderHandle_EmptyCustomAreaName() {
        // 测试自定义区域名称为空的情况
        WncFenceChangeRecordsEntity beforeRecord = new WncFenceChangeRecordsEntity();
        beforeRecord.setId(1L);
        beforeRecord.setFenceId(100);
        beforeRecord.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue());
        beforeRecord.setFenceStoreNo(1001);
        beforeRecord.setFenceAreaNo(2001);

        WncFenceAreaChangeRecordsEntity areaRecord = new WncFenceAreaChangeRecordsEntity();
        areaRecord.setId(1L);
        areaRecord.setFenceId(100);
        areaRecord.setCity("北京市");
        areaRecord.setArea(""); // 空区域名称
        areaRecord.setCustomAreaName("自定义区域"); // 有自定义区域名称
        beforeRecord.setAreaChangeRecords(Arrays.asList(areaRecord));

        WncFenceChangeRecordsEntity afterRecord = new WncFenceChangeRecordsEntity();
        afterRecord.setId(2L);
        afterRecord.setFenceId(100);
        afterRecord.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
        afterRecord.setFenceStoreNo(1002);
        afterRecord.setFenceAreaNo(2002);

        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(1L))
                .thenReturn(Arrays.asList(beforeRecord, afterRecord));

        // 模拟OFC查询返回订单
        FulfillmentOrderDTO order = new FulfillmentOrderDTO();
        order.setOuterOrderId("TEST002");
        order.setCity("北京市");
        order.setArea("自定义区域");
        order.setPoi("116.123,39.456");
        when(ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(any(FulfillmentQueryInput.class)))
                .thenReturn(Arrays.asList(order));

        // 模拟保存订单成功
        when(fenceChangeTaskDetailRepository.saveBatch(any())).thenReturn(true);

        service.customFenceChangeOrderHandle(testTask);

        verify(ofcQueryFacade).queryTimePlus2WaitFulfillmentOrder(any(FulfillmentQueryInput.class));
        verify(fenceChangeTaskDetailRepository).saveBatch(any());
    }
}
