package net.summerfarm.wnc.application.service.changeTask;

import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * 切仓任务订单处理服务测试
 */
@ExtendWith(MockitoExtension.class)
class FenceChangTaskForOrderChangeHandleServiceTest {

    @Mock
    private OfcQueryFacade ofcQueryFacade;
    
    @Mock
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    
    @Mock
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    
    @Mock
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    @InjectMocks
    private FenceChangTaskForOrderChangeHandleService service;

    private FenceChangeTaskEntity testTask;
    private List<WncFenceChangeRecordsEntity> testFenceChangeRecords;

    @BeforeEach
    void setUp() {
        // 创建测试任务
        testTask = new FenceChangeTaskEntity();
        testTask.setId(1L);
        testTask.setFenceId(100);
        testTask.setExeTime(LocalDateTime.now());
        testTask.setStatus(FenceChangeTaskEnums.Status.WAIT);

        // 创建测试围栏变更记录
        WncFenceChangeRecordsEntity beforeRecord = new WncFenceChangeRecordsEntity();
        beforeRecord.setId(1L);
        beforeRecord.setFenceId(100);
        beforeRecord.setFenceStoreNo(1001); // 变更前城配仓
        beforeRecord.setFenceAreaNo(2001); // 变更前运营区域
        beforeRecord.setFenceChangeStage(0); // 变更前

        WncFenceAreaChangeRecordsEntity areaRecord = new WncFenceAreaChangeRecordsEntity();
        areaRecord.setCity("北京市");
        areaRecord.setArea("朝阳区");
        beforeRecord.setAreaChangeRecords(Arrays.asList(areaRecord));

        WncFenceChangeRecordsEntity afterRecord = new WncFenceChangeRecordsEntity();
        afterRecord.setId(2L);
        afterRecord.setFenceId(100);
        afterRecord.setFenceStoreNo(1002); // 变更后城配仓
        afterRecord.setFenceAreaNo(2002); // 变更后运营区域
        afterRecord.setFenceChangeStage(1); // 变更后

        testFenceChangeRecords = Arrays.asList(beforeRecord, afterRecord);
    }

    @Test
    void testNormalFenceChangeOrderHandle_NoAreaChange() {
        // 模拟无区域变更的情况
        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(anyLong()))
                .thenReturn(Collections.emptyList());

        service.normalFenceChangeOrderHandle(testTask);

        // 验证任务被直接完结
        verify(fenceChangeTaskRepository).update(any(FenceChangeTaskEntity.class));
    }

    @Test
    void testNormalFenceChangeOrderHandle_WithAreaChange() {
        // 模拟有区域变更的情况
        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(anyLong()))
                .thenReturn(testFenceChangeRecords);

        // 模拟查询到履约单数据
        FulfillmentOrderDTO fulfillmentOrder = new FulfillmentOrderDTO();
        fulfillmentOrder.setOuterOrderId("TEST001");
        fulfillmentOrder.setCity("北京市");
        fulfillmentOrder.setArea("朝阳区");
        fulfillmentOrder.setStoreNo(1001);
        fulfillmentOrder.setDeliveryTime(LocalDate.now().plusDays(3));

        when(ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(any(FulfillmentQueryInput.class)))
                .thenReturn(Arrays.asList(fulfillmentOrder));

        service.normalFenceChangeOrderHandle(testTask);

        // 验证调用了保存订单明细和更新任务状态
        verify(fenceChangeTaskDetailRepository).saveBatch(any());
        verify(fenceChangeTaskRepository).update(any(FenceChangeTaskEntity.class));
    }

    @Test
    void testNormalFenceChangeOrderHandle_NullTask() {
        service.normalFenceChangeOrderHandle(null);

        // 验证没有任何操作
        verifyNoInteractions(wncFenceChangeRecordsQueryRepository);
        verifyNoInteractions(fenceChangeTaskRepository);
    }
}
