package net.summerfarm.wnc.application.service.changeTask.factory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.context.FenceChangeContext;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2025/9/4 17:57<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class FenceChangeTaskContextFactory {

    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    /**
     * 构建围栏变更上下文
     */
    public FenceChangeContext buildFenceChangeContext(String changeBatchNo, List<WncCityAreaChangeWarehouseRecordsEntity> records) {
        String province = records.get(0).getProvince();
        String city = records.get(0).getCity();
        String area = records.get(0).getArea();

        // 查询切仓批次快照前后信息
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = wncFenceChangeRecordsQueryRepository.selectWithAreaByChangeBatchNo(changeBatchNo);
        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            log.warn("未找到changeBatchNo: {} 对应的围栏变更记录", changeBatchNo);
            return null;
        }

        // 分离变更前后的围栏记录
        List<WncFenceChangeRecordsEntity> afterFenceChangeRecords = fenceChangeRecords.stream()
                .filter(record -> record.getFenceChangeStage().equals(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(afterFenceChangeRecords)) {
            log.info("围栏切仓区域处理任务，无可执行围栏变更任务，changeBatchNo: {}", changeBatchNo);
            return null;
        }

        List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords = fenceChangeRecords.stream()
                .filter(record -> record.getFenceChangeStage().equals(WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue()))
                .collect(Collectors.toList());

        return FenceChangeContext.builder()
                .changeBatchNo(changeBatchNo)
                .records(records)
                .province(province)
                .city(city)
                .area(area)
                .beforeFenceChangeRecords(beforeFenceChangeRecords)
                .afterFenceChangeRecords(afterFenceChangeRecords)
                .build();
    }
}
