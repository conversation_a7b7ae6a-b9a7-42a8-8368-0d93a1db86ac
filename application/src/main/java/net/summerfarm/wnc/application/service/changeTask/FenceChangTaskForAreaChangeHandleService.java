package net.summerfarm.wnc.application.service.changeTask;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.context.FenceChangeContext;
import net.summerfarm.wnc.application.service.changeTask.factory.FenceChangeTaskContextFactory;
import net.summerfarm.wnc.application.service.fence.CityAreaChangeRecordHandlerService;
import net.summerfarm.wnc.client.mq.WncMqConstants;
import net.summerfarm.wnc.client.mq.msg.out.CustomFenceChangeHandleMsg;
import net.summerfarm.wnc.client.mq.msg.out.FenceChangeAreaHandleMsg;
import net.summerfarm.wnc.common.enums.*;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.fence.AdCodeMsgRepository;
import net.summerfarm.wnc.domain.fence.FenceDomainService;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.*;
import net.summerfarm.wnc.domain.fence.param.command.WncCityAreaChangeWarehouseRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.CustomFenceAreaEsCommandDomainService;
import net.summerfarm.wnc.domain.fence.service.WncCityAreaChangeWarehouseRecordsCommandDomainService;
import net.summerfarm.wnc.domain.fence.service.WncFenceAreaChangeRecordsCommandDomainService;
import net.summerfarm.wnc.domain.fence.service.WncFenceChangeRecordsCommandDomainService;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 围栏切仓任务-区域变更服务
 * date: 2025/9/2 11:38<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class FenceChangTaskForAreaChangeHandleService {

    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Resource
    private WncFenceChangeRecordsCommandDomainService wncFenceChangeRecordsCommandDomainService;
    @Resource
    private FenceRepository fenceRepository;
    @Resource
    private AdCodeMsgRepository adCodeMsgRepository;
    @Autowired
    private FenceDomainService fenceDomainService;
    @Autowired
    private CustomFenceAreaEsCommandDomainService customFenceAreaEsCommandDomainService;
    @Resource
    private WncFenceAreaChangeRecordsCommandDomainService wncFenceAreaChangeRecordsCommandDomainService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private CityAreaChangeRecordHandlerService cityAreaChangeRecordHandlerService;
    @Resource
    private WncCityAreaChangeWarehouseRecordsCommandDomainService wncCityAreaChangeWarehouseRecordsCommandDomainService;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskContextFactory fenceChangeTaskContextFactory;

    /**
     * 按批次执行自定义围栏切仓区域处理任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeCustomFenceChangeAreaHandleByBatch(String changeBatchNo, List<WncCityAreaChangeWarehouseRecordsEntity> records) {
        try {
            log.info("开始处理自定义围栏切仓区域任务，changeBatchNo: {}, 记录数: {}", changeBatchNo, records.size());

            // 1. 参数验证和基础信息提取
            FenceChangeContext context = fenceChangeTaskContextFactory.buildFenceChangeContext(changeBatchNo, records);
            if (context == null) {
                return;
            }

            // 2. 根据围栏变更类型执行相应的处理逻辑
            FenceChangeTypeEnum changeType = determineFenceChangeType(context.getBeforeFenceChangeRecords());

            // 3.根据围栏变更类型执行相应的处理逻辑
            this.executeCustomFenceChangeByType(changeType, context);

            log.info("完成处理自定义围栏切仓区域任务，changeBatchNo: {}", changeBatchNo);
        } catch (Exception e) {
            log.error("处理自定义围栏切仓区域任务失败，changeBatchNo: {}, 错误信息: {}", changeBatchNo, e.getMessage(), e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 确定围栏变更类型
     */
    private FenceChangeTypeEnum determineFenceChangeType(List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords) {
        if (CollectionUtils.isEmpty(beforeFenceChangeRecords)) {
            return FenceChangeTypeEnum.NONE_TO_CUSTOM;
        }

        List<FenceEntity> fenceEntityList = beforeFenceChangeRecords.stream()
                .map(WncFenceChangeRecordsEntity::getFenceDetailEntity)
                .collect(Collectors.toList());

        List<FenceEnums.Type> types = fenceEntityList.stream()
                .map(FenceEntity::getType)
                .distinct()
                .collect(Collectors.toList());

        if (types.size() == 1 && types.contains(FenceEnums.Type.CUSTOM)) {
            return FenceChangeTypeEnum.CUSTOM_TO_CUSTOM;
        } else if (types.size() > 1 && types.contains(FenceEnums.Type.CUSTOM)) {
            return FenceChangeTypeEnum.NORMAL_TO_CUSTOM;
        } else {
            return FenceChangeTypeEnum.UNKNOWN;
        }
    }

    /**
     * 根据围栏变更类型执行相应的处理逻辑
     */
    private void executeCustomFenceChangeByType(FenceChangeTypeEnum changeType, FenceChangeContext context) {
        switch (changeType) {
            case CUSTOM_TO_CUSTOM:
                handleCustomToCustomFenceChange(context);
                break;
            case NORMAL_TO_CUSTOM:
                handleNormalToCustomFenceChange(context);
                break;
            case NONE_TO_CUSTOM:
                handleNoneToCustomFenceChange(context);
                break;
            default:
                log.error("\n围栏切仓区域处理任务，围栏类型异常\n，changeBatchNo: {}", context.getChangeBatchNo());
                break;
        }
    }

    /**
     * 按批次执行普通围栏切仓区域处理任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeNormalFenceChangeAreaHandleByBatch(String changeBatchNo, List<WncCityAreaChangeWarehouseRecordsEntity> records) {
        try {
            log.info("开始处理普通围栏切仓区域任务，changeBatchNo: {}, 记录数: {}", changeBatchNo, records.size());

            // 1. 参数验证和基础信息提取
            FenceChangeContext context = fenceChangeTaskContextFactory.buildFenceChangeContext(changeBatchNo, records);
            if (context == null) {
                return;
            }

            // 2. 根据围栏变更类型执行相应的处理逻辑
            handleNormalToNormalFenceChange(context);

            log.info("完成处理普通围栏切仓区域任务，changeBatchNo: {}", changeBatchNo);
        } catch (Exception e) {
            log.error("处理普通围栏切仓区域任务失败，changeBatchNo: {}, 错误信息: {}", changeBatchNo, e.getMessage(), e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 普通围栏 -> 普通围栏 处理
     */
    private void handleNormalToNormalFenceChange(FenceChangeContext context) {
        // 1.切仓任务状态变更、城市区域切仓记录状态变更
        this.cityAreaAndTaskChangeStatus(context.getRecords());

        // 2.查询需要更新围栏id的区域记录
        List<WncFenceAreaChangeRecordsEntity> needUpdateFenceIdAreaChangeRecords =
                this.findNeedUpdateFenceIdAreaChangeRecords(context.getAfterFenceChangeRecords());

        List<FenceChangeAreaHandleMsg> fenceChangeAreaHandleMsgs;
        if (!CollectionUtils.isEmpty(needUpdateFenceIdAreaChangeRecords)) {
            // 切区域
            fenceChangeAreaHandleMsgs = this.updateAreaChangeHandle(
                    context.getBeforeFenceChangeRecords(),
                    context.getAfterFenceChangeRecords(),
                    context.getProvince(),
                    needUpdateFenceIdAreaChangeRecords);
        } else {
            // 切城配仓
            fenceChangeAreaHandleMsgs = this.updateStoreNoChangeHandle(
                    context.getBeforeFenceChangeRecords(),
                    context.getAfterFenceChangeRecords(),
                    context.getProvince());
        }

        // 3.如果没有变化，直接完成任务
        if (CollectionUtils.isEmpty(fenceChangeAreaHandleMsgs)) {
            completeTaskWithoutChanges(context.getRecords());
        }

        // 4.发送消息
        sendNormalFenceChangeMessages(fenceChangeAreaHandleMsgs, context.getAfterFenceChangeRecords());
    }

    /**
     * 完成没有变化的任务
     */
    private void completeTaskWithoutChanges(List<WncCityAreaChangeWarehouseRecordsEntity> records) {
        List<Long> fenceChangeTaskIds = records.stream()
                .map(WncCityAreaChangeWarehouseRecordsEntity::getFenceChangeTaskId)
                .collect(Collectors.toList());

        // 切仓任务状态变更【订单切换中】->【已完成】
        fenceChangeTaskDomainService.updateFenceChangeTaskStatus(fenceChangeTaskIds,
                FenceChangeTaskEnums.Status.ORDER_CHANGE_ING.getValue(),
                FenceChangeTaskEnums.Status.COMPLETED.getValue());
    }

    /**
     * 发送普通围栏切仓消息
     */
    private void sendNormalFenceChangeMessages(List<FenceChangeAreaHandleMsg> fenceChangeAreaHandleMsgs,
                                             List<WncFenceChangeRecordsEntity> afterFenceChangeRecords) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    if (CollectionUtils.isEmpty(fenceChangeAreaHandleMsgs)) {
                        log.info("围栏切仓任务编号：{}，无消息需要发送",
                                afterFenceChangeRecords.get(0).getFenceChangeTaskId());
                        return;
                    }
                    for (FenceChangeAreaHandleMsg fenceChangeAreaHandleMsg : fenceChangeAreaHandleMsgs) {
                        mqProducer.send(WncMqConstants.Topic.TOPIC_WNC_FENCE,
                                WncMqConstants.Tag.TAG_FENCE_CHANGE_AREA_HANDLE,
                                fenceChangeAreaHandleMsg);
                    }
                } catch (Throwable e) {
                    log.error("围栏切仓任务编号：{}区域切换处理发送消息失败，异常原因：{}",
                            afterFenceChangeRecords.get(0).getFenceChangeTaskId(), e.getMessage(), e);
                }
            }
        });
    }

    private void cityAreaAndTaskChangeStatus(List<WncCityAreaChangeWarehouseRecordsEntity> records) {
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        List<Long> cityAreaChangeIds = records.stream().map(WncCityAreaChangeWarehouseRecordsEntity::getId).collect(Collectors.toList());
        List<Long> fenceChangeTaskIds = records.stream().map(WncCityAreaChangeWarehouseRecordsEntity::getFenceChangeTaskId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(fenceChangeTaskIds)){
            // 切仓任务状态变更【待执行】->【订单切换中】
            fenceChangeTaskDomainService.updateFenceChangeTaskStatus(fenceChangeTaskIds,
                    FenceChangeTaskEnums.Status.WAIT.getValue(),
                    FenceChangeTaskEnums.Status.ORDER_CHANGE_ING.getValue());
        }

        if (CollectionUtils.isEmpty(cityAreaChangeIds)){
            // 城市区域切仓记录状态变更【待生效】->【生效中】
            wncCityAreaChangeWarehouseRecordsCommandDomainService.updateCityAreaChangeStatus(cityAreaChangeIds,
                    WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue(),
                    WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE.getValue());
        }
    }

    /**
     * 创建结束围栏区域快照信息、变更城市区域记录状态
     * @param city 城市
     * @param area 区域
     * @param beforeFenceChangeRecords 变更前围栏记录
     */
    private void effectiveCityAreaEndSnapshotCreateStatusChange(String city, String area, List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords) {
        // 查询此城市区域正在生效的城市区域
        List<WncCityAreaChangeWarehouseRecordsEntity> effectiveCityAreaChangeWarehouseRecords = wncCityAreaChangeWarehouseRecordsQueryRepository.selectByCityAreaChangeStage(city, area, WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE);
        WncCityAreaChangeWarehouseRecordsEntity effectiveCityAreaChangeWarehouseRecord = effectiveCityAreaChangeWarehouseRecords.get(0);

        String effectiveChangeBatchNo = effectiveCityAreaChangeWarehouseRecord.getChangeBatchNo();
        Long effectiveFenceChangeTaskId = effectiveCityAreaChangeWarehouseRecord.getFenceChangeTaskId();

        List<Integer> fenceIds = beforeFenceChangeRecords.stream().map(WncFenceChangeRecordsEntity::getFenceId).collect(Collectors.toList());

        // 创建结束围栏区域快照信息
        cityAreaChangeRecordHandlerService.createFenceAreaRecord(fenceIds, effectiveChangeBatchNo, effectiveFenceChangeTaskId, WncFenceChangeRecordsEnums.FenceChangeStage.END);

        // 变更城市区域记录状态为已结束
        WncCityAreaChangeWarehouseRecordsCommandParam param = new WncCityAreaChangeWarehouseRecordsCommandParam();
        param.setId(effectiveCityAreaChangeWarehouseRecord.getId());
        param.setChangeStatus(WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.END.getValue());
        wncCityAreaChangeWarehouseRecordsCommandDomainService.update(param);
    }

    /**
     * 处理城配仓切换
     *
     * @param beforeFenceChangeRecords 变更前围栏记录
     * @param afterFenceChangeRecords  变更后围栏记录
     * @param province                 省份
     * @return 城配仓切换消息
     */
    private List<FenceChangeAreaHandleMsg> updateStoreNoChangeHandle(List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords, List<WncFenceChangeRecordsEntity> afterFenceChangeRecords, String province) {
        List<FenceChangeAreaHandleMsg> fenceChangeAreaHandleMsgs = new ArrayList<>();

        // 更新城配仓
        List<Integer> fenceChangeStoreNoFenceIds = this.updateFenceStoreNoChangeHandle(beforeFenceChangeRecords, afterFenceChangeRecords);

        // 存在切城配仓，构建切仓消息
        if(!CollectionUtils.isEmpty(fenceChangeStoreNoFenceIds)){
            fenceChangeAreaHandleMsgs = this.buildFenceStoreNoChangeMsg(beforeFenceChangeRecords, afterFenceChangeRecords, province);
        }
        return fenceChangeAreaHandleMsgs;
    }

    /**
     * 构建城配仓变更消息
     * @param beforeFenceChangeRecords 变更前围栏记录
     * @param afterFenceChangeRecords 变更后围栏记录
     * @param province 省份
     * @return 城配仓变更消息
     */
    private List<FenceChangeAreaHandleMsg> buildFenceStoreNoChangeMsg(List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords, List<WncFenceChangeRecordsEntity> afterFenceChangeRecords, String province) {
        // 过滤出有围栏id的记录
        List<WncFenceChangeRecordsEntity> beforeHaveFenceIdRecords = beforeFenceChangeRecords.stream()
                .filter(e -> e.getFenceId() != null)
                .collect(Collectors.toList());

        List<WncFenceChangeRecordsEntity> afterHaveFenceIdRecords = afterFenceChangeRecords.stream()
                .filter(e -> e.getFenceId() != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(beforeHaveFenceIdRecords) || CollectionUtils.isEmpty(afterHaveFenceIdRecords)) {
            return null;
        }
        // 根据围栏id分组，判断城配仓是否有变化
        Map<Integer, List<WncFenceChangeRecordsEntity>> beforeFenceIdMap = beforeHaveFenceIdRecords.stream().collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getFenceId));
        Map<Integer, List<WncFenceChangeRecordsEntity>> afterFenceIdMap = afterHaveFenceIdRecords.stream().collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getFenceId));

        List<FenceChangeAreaHandleMsg> fenceChangeAreaHandleMsgList = new ArrayList<>();
        
        beforeFenceIdMap.forEach((fenceId, beforeRecords) -> {
            List<WncFenceChangeRecordsEntity> afterRecords = afterFenceIdMap.get(fenceId);
            if (CollectionUtils.isEmpty(afterRecords)) {
                return;
            }
            // 判断城配仓是否有变化
            WncFenceChangeRecordsEntity beforeFenceChangeRecord = beforeRecords.get(0);
            WncFenceChangeRecordsEntity afterFenceChangeRecord = afterRecords.get(0);

            if (!Objects.equals(beforeFenceChangeRecord.getFenceStoreNo(), afterFenceChangeRecord.getFenceStoreNo())) {

                List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecords = beforeFenceChangeRecord.getAreaChangeRecords();

                List<FenceChangeAreaHandleMsg> fenceChangeAreaHandleMsgs = beforeAreaChangeRecords.stream().map(areaChange -> {
                    FenceChangeAreaHandleMsg msg = new FenceChangeAreaHandleMsg();

                    msg.setFenceProvince(province);
                    msg.setFenceCity(areaChange.getCity());
                    msg.setFenceAreas(Collections.singletonList(areaChange.getArea()));

                    msg.setOldStoreNo(beforeFenceChangeRecord.getFenceStoreNo());
                    msg.setOldAreaNo(beforeFenceChangeRecord.getFenceAreaNo());

                    msg.setNewStoreNo(afterFenceChangeRecord.getFenceStoreNo());
                    msg.setNewAreaNo(afterFenceChangeRecord.getFenceAreaNo());

                    return msg;
                }).collect(Collectors.toList());

                fenceChangeAreaHandleMsgList.addAll(fenceChangeAreaHandleMsgs);
            }
        });
        
        return fenceChangeAreaHandleMsgList;
    }

    /**
     * 处理区域切换
     *
     * @param beforeFenceChangeRecords           变更前围栏记录
     * @param afterFenceChangeRecords            变更后围栏记录
     * @param province                           省份
     * @param needUpdateFenceIdAreaChangeRecords 需要更新围栏Id的区域
     * @return 围栏区域切换消息
     */
    private List<FenceChangeAreaHandleMsg> updateAreaChangeHandle(List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords , List<WncFenceChangeRecordsEntity> afterFenceChangeRecords, String province, List<WncFenceAreaChangeRecordsEntity> needUpdateFenceIdAreaChangeRecords) {
        if (CollectionUtils.isEmpty(afterFenceChangeRecords) || CollectionUtils.isEmpty(beforeFenceChangeRecords)) {
            return null;
        }
        // 1.更新区域围栏id
        this.updateAreaFenceIdAndSendMsg(needUpdateFenceIdAreaChangeRecords);

        // 2.发送消息通知商城侧
        Map<Integer, WncFenceChangeRecordsEntity> fenceIdToBeforeFenceChangeRecordsMap = beforeFenceChangeRecords.stream().collect(Collectors.toMap(WncFenceChangeRecordsEntity::getFenceId, e -> e, (o, n) -> n));
        Map<Integer, WncFenceChangeRecordsEntity> fenceIdToAfterFenceChangeRecordsMap = afterFenceChangeRecords.stream().collect(Collectors.toMap(WncFenceChangeRecordsEntity::getFenceId, e -> e, (o, n) -> n));

        // 消息组建
        return needUpdateFenceIdAreaChangeRecords.stream().map(needUpdateRecord -> {
            WncFenceChangeRecordsEntity beforeFenceChangeRecord = fenceIdToBeforeFenceChangeRecordsMap.get(needUpdateRecord.getAdCodeMsgId());
            WncFenceChangeRecordsEntity afterFenceChangeRecord = fenceIdToAfterFenceChangeRecordsMap.get(needUpdateRecord.getFenceId());

            if (!Objects.equals(beforeFenceChangeRecord.getFenceStoreNo(), afterFenceChangeRecord.getFenceStoreNo())
                    || !Objects.equals(beforeFenceChangeRecord.getFenceAreaNo(), afterFenceChangeRecord.getFenceAreaNo())) {
                FenceChangeAreaHandleMsg msg = new FenceChangeAreaHandleMsg();

                msg.setFenceProvince(province);
                msg.setFenceCity(needUpdateRecord.getCity());
                msg.setFenceAreas(Collections.singletonList(needUpdateRecord.getArea()));

                msg.setOldStoreNo(beforeFenceChangeRecord.getFenceStoreNo());
                msg.setOldAreaNo(beforeFenceChangeRecord.getFenceAreaNo());

                msg.setNewStoreNo(afterFenceChangeRecord.getFenceStoreNo());
                msg.setNewAreaNo(afterFenceChangeRecord.getFenceAreaNo());

                return msg;
            } else {
                return null;
            }
        }).filter(Objects::nonNull
        ).collect(Collectors.toList());
    }

    private void updateAreaFenceIdAndSendMsg(List<WncFenceAreaChangeRecordsEntity> needUpdateFenceIdAreaChangeRecords) {
        // 查询被切仓到的围栏数据
        List<Integer> needChangeToTargetFenceIds = needUpdateFenceIdAreaChangeRecords.stream().map(WncFenceAreaChangeRecordsEntity::getFenceId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(needChangeToTargetFenceIds)){
            log.error("\n新建围栏切新建（拆分）围栏不存在围栏Id信息\n");
            return;
        }

        List<FenceEntity> areaChangeToTargetFenceList = fenceRepository.queryList(FenceQuery.builder().fenceIds(needChangeToTargetFenceIds).build());
        if (CollectionUtils.isEmpty(areaChangeToTargetFenceList)) {
            log.error("\n新建围栏切新建（拆分）围栏不存在围栏信息,fenceIds:{}\n",needChangeToTargetFenceIds);
            return;
        }
        Map<Integer, FenceEntity> changeToTargetfenceIdToFenceMap = areaChangeToTargetFenceList.stream().collect(Collectors.toMap(FenceEntity::getId, e -> e));

        List<AdCodeMsgEntity> needUpdateFenceIdAdCodeMsgList = needUpdateFenceIdAreaChangeRecords.stream().map(e -> {
            FenceEntity fenceEntity = changeToTargetfenceIdToFenceMap.get(e.getFenceId());
            FenceEnums.Status status = fenceEntity.getStatus();
            if (status == null) {
                log.error("\n新建围栏切新建（拆分）围栏不存在围栏状态信息,fenceId:{}\n",e.getFenceId());
                throw new BizException("围栏不存在状态信息");
            }
            AdCodeMsgEntity adCodeMsgEntity = new AdCodeMsgEntity();
            adCodeMsgEntity.setId(e.getAdCodeMsgId());
            adCodeMsgEntity.setFenceId(e.getFenceId());
            adCodeMsgEntity.setStatus(status.getValue());

            return adCodeMsgEntity;
        }).collect(Collectors.toList());

        fenceDomainService.updateFenceIdById(needUpdateFenceIdAdCodeMsgList);
    }

    /**
     * 查询需要更新围栏id的区域记录
     * @param afterFenceChangeRecords 变更后围栏记录
     * @return 需要更新围栏id的区域记录
     */
    private List<WncFenceAreaChangeRecordsEntity> findNeedUpdateFenceIdAreaChangeRecords(List<WncFenceChangeRecordsEntity> afterFenceChangeRecords) {
        if(CollectionUtils.isEmpty(afterFenceChangeRecords)){
            return null;
        }
        List<WncFenceAreaChangeRecordsEntity> fenceAreaChangeRecordsEntities = afterFenceChangeRecords.stream()
                .map(WncFenceChangeRecordsEntity::getAreaChangeRecords)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(fenceAreaChangeRecordsEntities)){
            return null;
        }

        // 查询当前数据库区域信息
        List<Integer> currentDBAdCodeMsgIds = fenceAreaChangeRecordsEntities.stream().map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(currentDBAdCodeMsgIds)){
            log.error("\n新建围栏切新建（拆分）围栏不存在区域Id信息\n");
            return null;
        }

        List<AdCodeMsgEntity> currentDBAdCodeMsgList = adCodeMsgRepository.queryList(AdCodeMsgQuery.builder().areaIds(currentDBAdCodeMsgIds).build());
        if (CollectionUtils.isEmpty(currentDBAdCodeMsgList)){
            log.error("\n新建围栏切新建（拆分）围栏不存在区域信息,currentDBAdCodeMsgIds:{}\n",currentDBAdCodeMsgIds);
            return null;
        }
        Map<Integer, AdCodeMsgEntity> currentDBAdCodeMsgIdToEntityMap = currentDBAdCodeMsgList.stream().collect(Collectors.toMap(AdCodeMsgEntity::getId, e -> e));

        // 过滤出变更了围栏的区域
        return fenceAreaChangeRecordsEntities.stream()
                .filter(e -> !Objects.equals(e.getFenceId(), currentDBAdCodeMsgIdToEntityMap.get(e.getAdCodeMsgId()).getFenceId()))
                .collect(Collectors.toList());
    }

    /**
     * 城配仓变更处理
     *
     * @param beforeFenceChangeRecords 变更前围栏记录
     * @param afterFenceChangeRecords  变更后围栏记录
     * @return 城配仓变更的围栏id集合
     */
    private List<Integer> updateFenceStoreNoChangeHandle(List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords, List<WncFenceChangeRecordsEntity> afterFenceChangeRecords) {
        if (CollectionUtils.isEmpty(beforeFenceChangeRecords) || CollectionUtils.isEmpty(afterFenceChangeRecords)) {
            return null;
        }
        // 过滤出有围栏id的记录
        List<WncFenceChangeRecordsEntity> beforeHaveFenceIdRecords = beforeFenceChangeRecords.stream()
                .filter(e -> e.getFenceId() != null)
                .collect(Collectors.toList());

        List<WncFenceChangeRecordsEntity> afterHaveFenceIdRecords = afterFenceChangeRecords.stream()
                .filter(e -> e.getFenceId() != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(beforeHaveFenceIdRecords) || CollectionUtils.isEmpty(afterHaveFenceIdRecords)) {
            return null;
        }

        // 根据围栏id分组，判断城配仓是否有变化
        Map<Integer, List<WncFenceChangeRecordsEntity>> beforeFenceIdMap = beforeHaveFenceIdRecords.stream().collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getFenceId));
        Map<Integer, List<WncFenceChangeRecordsEntity>> afterFenceIdMap = afterHaveFenceIdRecords.stream().collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getFenceId));

        List<Integer> fenceChangeStoreNoFenceIds = new ArrayList<>();
        beforeFenceIdMap.forEach((fenceId, beforeRecords) -> {
            List<WncFenceChangeRecordsEntity> afterRecords = afterFenceIdMap.get(fenceId);
            if (CollectionUtils.isEmpty(afterRecords)) {
                return;
            }
            // 判断城配仓是否有变化
            WncFenceChangeRecordsEntity beforeFenceChangeRecord = beforeRecords.get(0);
            WncFenceChangeRecordsEntity afterFenceChangeRecord = afterRecords.get(0);

            if (!Objects.equals(beforeFenceChangeRecord.getFenceStoreNo(), afterFenceChangeRecord.getFenceStoreNo())) {
                fenceDomainService.updateFenceStoreNo(fenceId, afterFenceChangeRecord.getFenceStoreNo());
                fenceChangeStoreNoFenceIds.add(fenceId);
            }
        });

        return fenceChangeStoreNoFenceIds;
    }

    /**
     * 普通围栏 ---> 自定义围栏 解绑围栏并设置为失效
     * @param beforeFenceChangeRecords 变更前围栏记录
     * @param afterFenceChangeRecords 变更后围栏记录
     */
    private void normalChangeToCustomFenceUnbindInvalidArea(List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords, List<WncFenceChangeRecordsEntity> afterFenceChangeRecords) {

        List<WncFenceChangeRecordsEntity> beforeHaveFenceIdRecords = beforeFenceChangeRecords.stream()
                .filter(e -> e.getFenceId() != null)
                .collect(Collectors.toList());

        List<WncFenceChangeRecordsEntity> afterHaveFenceIdRecords = afterFenceChangeRecords.stream()
                .filter(e -> e.getFenceId() != null)
                .collect(Collectors.toList());

        List<WncFenceAreaChangeRecordsEntity> areaChangeRecordsBefore = beforeHaveFenceIdRecords.get(0).getAreaChangeRecords();
        List<WncFenceAreaChangeRecordsEntity> areaChangeRecordsAfter = afterHaveFenceIdRecords.get(0).getAreaChangeRecords();

        List<Integer> areaChangeRecordIdsAfter = areaChangeRecordsAfter.stream().map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId).collect(Collectors.toList());

        List<WncFenceAreaChangeRecordsEntity> needUnbindAreaChangeRecords = areaChangeRecordsBefore.stream()
                .filter(e -> !areaChangeRecordIdsAfter.contains(e.getAdCodeMsgId()))
                .collect(Collectors.toList());

        List<Integer> needUnbindAreaAdCodeMsgId = needUnbindAreaChangeRecords.stream()
                .map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId)
                .collect(Collectors.toList());

        fenceDomainService.unbindAndInvalidAreaByAdCodeMsgIds(needUnbindAreaAdCodeMsgId);
    }

    /**
     * 创建新区域并回写ID
     * @param afterFenceChangeRecords 变更后围栏记录
     */
    private void createNewAreaWithWriteBackId(List<WncFenceChangeRecordsEntity> afterFenceChangeRecords) {
        if (CollectionUtils.isEmpty(afterFenceChangeRecords)) {
            log.info("围栏切仓区域处理任务，无可执行创建区域任务");
            return;
        }
        afterFenceChangeRecords.forEach(afterFenceChangeRecord -> {
            List<WncFenceAreaChangeRecordsEntity> areaChangeRecords = afterFenceChangeRecord.getAreaChangeRecords();
            if (CollectionUtils.isEmpty(areaChangeRecords)) {
                return;
            }
            Integer fenceId = afterFenceChangeRecord.getFenceId();

            List<AdCodeMsgEntity> adCodeMsgEntities = areaChangeRecords.stream().map(e -> {
                AdCodeMsgEntity adCodeMsgDetail = e.getAdCodeMsgDetailEntity();

                AdCodeMsgEntity adCodeMsgEntity = new AdCodeMsgEntity();
                adCodeMsgEntity.setAdCode(adCodeMsgDetail.getAdCode());
                adCodeMsgEntity.setProvince(adCodeMsgDetail.getProvince());
                adCodeMsgEntity.setCity(e.getCity());
                adCodeMsgEntity.setArea(e.getArea());
                adCodeMsgEntity.setLevel(adCodeMsgDetail.getLevel());
                adCodeMsgEntity.setGdId(adCodeMsgDetail.getGdId());
                adCodeMsgEntity.setStatus(adCodeMsgDetail.getStatus());
                adCodeMsgEntity.setFenceId(fenceId);
                adCodeMsgEntity.setCustomAreaName(e.getCustomAreaName());
                adCodeMsgEntity.setAreaType(AdCodeMsgEnums.AreaType.CUSTOM_AREA.getValue());
                adCodeMsgEntity.setAreaDrawType(e.getAreaDrawType());
                adCodeMsgEntity.setFenceAreaChangeId(e.getId());

                return adCodeMsgEntity;
            }).collect(Collectors.toList());

            // 生成区域并绑定围栏id
            fenceDomainService.createAreaBindingFence(adCodeMsgEntities);

            // 回写adCodeId、fenceId到快照表
            List<WncFenceAreaChangeRecordsCommandParam> params = areaChangeRecords.stream().map(e -> {
                WncFenceAreaChangeRecordsCommandParam param = new WncFenceAreaChangeRecordsCommandParam();
                param.setId(e.getId());
                param.setFenceId(e.getFenceId());
                param.setAdCodeMsgId(e.getAdCodeMsgId());
                return param;
            }).collect(Collectors.toList());

            // 更新围栏区域变更记录的围栏ID和ACM区域ID
            wncFenceAreaChangeRecordsCommandDomainService.updateAdCodeIdAndFenceId(params);
        });
    }

    private void fenceUnbindAndInvalidArea(List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords) {
        if (CollectionUtils.isEmpty(beforeFenceChangeRecords)) {
            log.info("围栏切仓区域处理任务，无可执行解绑任务");
            return;
        }
        List<Integer> fenceIds = beforeFenceChangeRecords.stream().map(WncFenceChangeRecordsEntity::getFenceId).collect(Collectors.toList());
        fenceDomainService.unbindAndInvalidAreaByFenceIds(fenceIds);
    }

    /**
     * 创建没有围栏ID的记录并回写围栏id
     * @param afterFenceChangeRecords 变更后围栏记录
     */
    private void createFenceForNoFenceIdRecordsWithWriteBackFenceId(List<WncFenceChangeRecordsEntity> afterFenceChangeRecords) {
        List<WncFenceChangeRecordsEntity> noFenceIdRecords = afterFenceChangeRecords.stream()
                .filter(fenceRecords -> fenceRecords.getFenceId() == null)
                .collect(Collectors.toList());

        // 没有围栏ID的数据进行创建
        noFenceIdRecords.forEach(noFenceIdChangeRecord -> {
            FenceEntity fenceDetailEntity = noFenceIdChangeRecord.getFenceDetailEntity();
            // 创建围栏
            fenceDomainService.createFence(fenceDetailEntity);

            // 更新变更上面的围栏信息
            WncFenceChangeRecordsCommandParam param = new WncFenceChangeRecordsCommandParam();
            param.setId(noFenceIdChangeRecord.getId());
            param.setFenceId(fenceDetailEntity.getId());
            wncFenceChangeRecordsCommandDomainService.update(param);

            noFenceIdChangeRecord.setFenceId(fenceDetailEntity.getId());
        });

    }

    /**
     * 自定义围栏 -> 自定义围栏 处理
     */
    private void handleCustomToCustomFenceChange(FenceChangeContext context) {
        // 1.历史自定义围栏生成结束时快照信息、并将【生效】的城市区域状态更新为【已结束】
        this.effectiveCityAreaEndSnapshotCreateStatusChange(context.getCity(), context.getArea(), context.getBeforeFenceChangeRecords());

        // 2.切仓任务状态变更【待执行】->【订单切换中】、城市区域切仓记录状态变更【待生效】->【生效中】
        this.cityAreaAndTaskChangeStatus(context.getRecords());

        // 3.有围栏id快照数据的旧自定义围栏上的城配仓的变更处理
        this.updateFenceStoreNoChangeHandle(context.getBeforeFenceChangeRecords(), context.getAfterFenceChangeRecords());

        // 4.没有围栏ID数据的创建、并回写围栏id到快照记录表中
        this.createFenceForNoFenceIdRecordsWithWriteBackFenceId(context.getAfterFenceChangeRecords());

        // 5.历史围栏区域解绑并设置为失效
        this.fenceUnbindAndInvalidArea(context.getBeforeFenceChangeRecords());

        // 6.重新生成新区域、回写fenceId、adCodeMsgId到快照记录表中
        this.createNewAreaWithWriteBackId(context.getAfterFenceChangeRecords());

        // 7.区域geo-shape需要保存到es里面
        List<Long> fenceChangeRecordIds = context.getAfterFenceChangeRecords().stream()
                .map(WncFenceChangeRecordsEntity::getId)
                .collect(Collectors.toList());
        customFenceAreaEsCommandDomainService.createCustomFenceArea(fenceChangeRecordIds);

        // 8.发送切仓消息
        sendCustomFenceChangeMessage(context);
    }

    /**
     * 普通围栏 -> 自定义围栏 处理
     */
    private void handleNormalToCustomFenceChange(FenceChangeContext context) {
        // 1.切仓任务状态变更、城市区域切仓记录状态变更
        this.cityAreaAndTaskChangeStatus(context.getRecords());

        // 2.围栏区域->自定义区域的需要解绑围栏并设置为失效
        this.normalChangeToCustomFenceUnbindInvalidArea(context.getBeforeFenceChangeRecords(), context.getAfterFenceChangeRecords());

        // 3.没有围栏ID数据的创建、并回写围栏id到快照记录表中
        this.createFenceForNoFenceIdRecordsWithWriteBackFenceId(context.getAfterFenceChangeRecords());

        // 4.重新生成新区域、回写fenceId、adCodeMsgId到快照记录表中
        this.createNewAreaWithWriteBackId(context.getAfterFenceChangeRecords());

        // 5.区域geo-shape需要保存到es里面
        List<Long> fenceChangeRecordIds = context.getAfterFenceChangeRecords().stream()
                .map(WncFenceChangeRecordsEntity::getId)
                .collect(Collectors.toList());
        customFenceAreaEsCommandDomainService.createCustomFenceArea(fenceChangeRecordIds);

        // 6.发送切仓消息
        sendCustomFenceChangeMessage(context);
    }

    /**
     * 无围栏 -> 自定义围栏 处理
     */
    private void handleNoneToCustomFenceChange(FenceChangeContext context) {
        // 1.切仓任务状态变更、城市区域切仓记录状态变更
        this.cityAreaAndTaskChangeStatus(context.getRecords());

        List<Long> fenceChangeTaskIds = context.getRecords().stream()
                .map(WncCityAreaChangeWarehouseRecordsEntity::getFenceChangeTaskId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(fenceChangeTaskIds)) {
            // 切仓任务状态变更【订单切换中】->【已完成】，直接将任务完结
            fenceChangeTaskDomainService.updateFenceChangeTaskStatus(fenceChangeTaskIds,
                    FenceChangeTaskEnums.Status.ORDER_CHANGE_ING.getValue(),
                    FenceChangeTaskEnums.Status.COMPLETED.getValue());
        }

        // 2.没有围栏ID数据的创建、并回写围栏id到快照记录表中
        this.createFenceForNoFenceIdRecordsWithWriteBackFenceId(context.getAfterFenceChangeRecords());

        // 3.重新生成新区域、回写fenceId、adCodeMsgId到快照记录表中
        this.createNewAreaWithWriteBackId(context.getAfterFenceChangeRecords());

        // 4.区域geo-shape需要保存到es里面
        List<Long> fenceChangeRecordIds = context.getAfterFenceChangeRecords().stream()
                .map(WncFenceChangeRecordsEntity::getId)
                .collect(Collectors.toList());
        customFenceAreaEsCommandDomainService.createCustomFenceArea(fenceChangeRecordIds);

        // 5.发送切仓消息
        sendCustomFenceChangeMessage(context);
    }

    /**
     * 发送自定义围栏切仓消息
     */
    private void sendCustomFenceChangeMessage(FenceChangeContext context) {
        CustomFenceChangeHandleMsg msg = new CustomFenceChangeHandleMsg();
        msg.setProvince(context.getProvince());
        msg.setCity(context.getCity());
        msg.setArea(context.getArea());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    mqProducer.send(WncMqConstants.Topic.TOPIC_WNC_FENCE, WncMqConstants.Tag.TAG_CUSTOM_FENCE_CHANGE_AREA_HANDLE, msg);
                } catch (Throwable e) {
                    log.error("自定义区域切仓消息发送失败，异常原因：{}，msg:{}", e.getMessage(), JSON.toJSON(msg), e);
                }
            }
        });
    }

}
