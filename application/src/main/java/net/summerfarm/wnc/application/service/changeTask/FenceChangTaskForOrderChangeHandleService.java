package net.summerfarm.wnc.application.service.changeTask;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.client.mq.msg.out.FenceChangeAreaHandleMsg;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskSender;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.WncCityAreaChangeWarehouseRecordsQueryDomainService;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 切仓任务订单处理服务
 * date: 2025/9/5 10:16<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForOrderChangeHandleService {

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskSender fenceChangeTaskSender;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Resource
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    /**
     * 普通围栏切仓订单处理
     */
    public void normalFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }

        // 根据切仓任务ID查询切仓变更批次
        List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities = wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(waitOrderChangeHandleTask.getId());

        // 变更前后对比，看城配仓是否有变化，运营区域是否有变化，有变化的需要把省市区给找出来构造省市区变更前的城配仓、运营区域、变更后的城配仓、运营区域

        // 然后根据省市区查询T+1之后待履约的履约单数据

        // 过滤出城配仓有变更的订单数据

        // 保存到切仓订单明细表

        // 判断需要切仓的订单，如果需要切仓的订单为空，直接完结任务,否则调用OFC进行切仓

        // 判断围栏是否发生变更，发生变更需要通知到OFC、todo需要OFC做支持

    }

}
