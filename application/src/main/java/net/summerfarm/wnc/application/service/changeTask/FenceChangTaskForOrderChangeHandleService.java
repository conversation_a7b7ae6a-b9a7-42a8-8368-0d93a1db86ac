package net.summerfarm.wnc.application.service.changeTask;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.client.mq.msg.out.FenceChangeAreaHandleMsg;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskSender;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.WncCityAreaChangeWarehouseRecordsQueryDomainService;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 切仓任务订单处理服务
 * date: 2025/9/5 10:16<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForOrderChangeHandleService {

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskSender fenceChangeTaskSender;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Resource
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    /**
     * 普通围栏切仓订单处理
     */
    public void normalFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }

        // 1.根据切仓任务ID查询切仓变更批次
        List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities = wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(waitOrderChangeHandleTask.getId());

        // 2.变更前后对比，看城配仓是否有变化，运营区域是否有变化，有变化的需要把省市区给找出来构造省市区变更前的城配仓、运营区域、变更后的城配仓、运营区域
        Map<String, AreaChangeInfo> areaChangeInfoMap = compareAndBuildAreaChangeInfo(fenceChangeRecordsEntities);
        if (areaChangeInfoMap.isEmpty()) {
            log.info("切仓任务ID:{} 无区域变更，直接完结任务", waitOrderChangeHandleTask.getId());
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));
            return;
        }

        // 3.然后根据省市区查询T+1之后待履约的履约单数据
        List<FulfillmentOrderDTO> fulfillmentOrders = queryFulfillmentOrders(areaChangeInfoMap, waitOrderChangeHandleTask);

        // 4.过滤出城配仓有变更的订单数据
        List<FulfillmentOrderDTO> needChangeOrders = filterNeedChangeOrders(fulfillmentOrders, areaChangeInfoMap);

        // 5.保存到切仓订单明细表
        List<FenceChangeTaskOrderEntity> taskOrderEntities = saveTaskOrderDetails(needChangeOrders, waitOrderChangeHandleTask.getId());

        // 6.判断需要切仓的订单，如果需要切仓的订单为空，直接完结任务,否则调用OFC进行切仓
        if (CollectionUtils.isEmpty(taskOrderEntities)) {
            log.info("切仓任务ID:{} 无需要切仓的订单，直接完结任务", waitOrderChangeHandleTask.getId());
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));
        } else {
            log.info("切仓任务ID:{} 需要切仓的订单数量:{}", waitOrderChangeHandleTask.getId(), taskOrderEntities.size());
            // 更新任务状态为订单切换中
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.ORDER_CHANGE_ING));
            // TODO: 调用OFC进行切仓 - 需要OFC做支持
        }

        // 判断围栏是否发生变更，发生变更需要通知到OFC、todo需要OFC做支持

    }

    /**
     * 比较变更前后的数据，构建区域变更信息
     */
    private Map<String, AreaChangeInfo> compareAndBuildAreaChangeInfo(List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities) {
        Map<String, AreaChangeInfo> areaChangeInfoMap = new HashMap<>();

        // 按围栏ID分组
        Map<Integer, List<WncFenceChangeRecordsEntity>> fenceRecordsMap = fenceChangeRecordsEntities.stream()
                .collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getFenceId));

        for (Map.Entry<Integer, List<WncFenceChangeRecordsEntity>> entry : fenceRecordsMap.entrySet()) {
            List<WncFenceChangeRecordsEntity> records = entry.getValue();

            // 分离变更前和变更后的记录
            WncFenceChangeRecordsEntity beforeRecord = records.stream()
                    .filter(r -> Objects.equals(r.getFenceChangeStage(), WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue())) // 0变更前
                    .findFirst().orElse(null);
            WncFenceChangeRecordsEntity afterRecord = records.stream()
                    .filter(r -> Objects.equals(r.getFenceChangeStage(), WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue())) // 0变更前
                    .findFirst().orElse(null);

            if (beforeRecord == null || afterRecord == null) {
                continue;
            }

            // 检查城配仓或运营区域是否有变化
            boolean storeChanged = !Objects.equals(beforeRecord.getFenceStoreNo(), afterRecord.getFenceStoreNo());
            boolean areaChanged = !Objects.equals(beforeRecord.getFenceAreaNo(), afterRecord.getFenceAreaNo());

            if (storeChanged || areaChanged) {
                // 获取该围栏的区域变更记录
                List<WncFenceAreaChangeRecordsEntity> areaChangeRecords = beforeRecord.getAreaChangeRecords();
                if (!CollectionUtils.isEmpty(areaChangeRecords)) {
                    for (WncFenceAreaChangeRecordsEntity areaRecord : areaChangeRecords) {
                        // 检查城市和区域是否为空
                        String city = areaRecord.getCity();
                        String area = areaRecord.getArea();

                        if (city == null || city.trim().isEmpty()) {
                            log.warn("围栏区域变更记录城市为空，跳过处理，记录ID:{}", areaRecord.getId());
                            continue;
                        }

                        // 区域可能为空，使用默认值或自定义区域名称
                        if (area == null || area.trim().isEmpty()) {
                            area = areaRecord.getCustomAreaName();
                            if (area == null || area.trim().isEmpty()) {
                                log.warn("围栏区域变更记录区域和自定义区域名称都为空，跳过处理，记录ID:{}", areaRecord.getId());
                                continue;
                            }
                        }

                        String cityAreaKey = city + "#" + area;
                        AreaChangeInfo changeInfo = new AreaChangeInfo();
                        changeInfo.setCity(city);
                        changeInfo.setArea(area);
                        changeInfo.setOldStoreNo(beforeRecord.getFenceStoreNo());
                        changeInfo.setNewStoreNo(afterRecord.getFenceStoreNo());
                        changeInfo.setOldAreaNo(beforeRecord.getFenceAreaNo());
                        changeInfo.setNewAreaNo(afterRecord.getFenceAreaNo());
                        changeInfo.setStoreChanged(storeChanged);
                        changeInfo.setAreaChanged(areaChanged);

                        areaChangeInfoMap.put(cityAreaKey, changeInfo);
                    }
                }
            }
        }

        return areaChangeInfoMap;
    }

    /**
     * 查询T+1之后待履约的履约单数据
     */
    private List<FulfillmentOrderDTO> queryFulfillmentOrders(Map<String, AreaChangeInfo> areaChangeInfoMap,
                                                           FenceChangeTaskEntity waitOrderChangeHandleTask) {
        List<FulfillmentOrderDTO> allFulfillmentOrders = new ArrayList<>();

        // 按城市分组查询
        Map<String, List<String>> cityAreasMap = areaChangeInfoMap.values().stream()
                .collect(Collectors.groupingBy(
                        AreaChangeInfo::getCity,
                        Collectors.mapping(AreaChangeInfo::getArea, Collectors.toList())
                ));

        for (Map.Entry<String, List<String>> entry : cityAreasMap.entrySet()) {
            String city = entry.getKey();
            List<String> areas = entry.getValue();

            // 获取该城市区域对应的城配仓编号（使用变更前的城配仓编号查询）
            Integer storeNo = areaChangeInfoMap.values().stream()
                    .filter(info -> city.equals(info.getCity()))
                    .map(AreaChangeInfo::getOldStoreNo)
                    .findFirst().orElse(null);

            if (storeNo != null) {
                FulfillmentQueryInput queryInput = FulfillmentQueryInput.builder()
                        .city(city)
                        .areas(areas)
                        .storeNo(storeNo)
                        .deliveryDateBegin(waitOrderChangeHandleTask.getExeTimePlus2Date()) // T+2的时间点
                        .build();

                try {
                    List<FulfillmentOrderDTO> fulfillmentOrders = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(queryInput);
                    if (!CollectionUtils.isEmpty(fulfillmentOrders)) {
                        allFulfillmentOrders.addAll(fulfillmentOrders);
                    }
                } catch (Exception e) {
                    log.error("查询履约单数据异常，城市:{}, 区域:{}, 城配仓:{}, 异常信息:{}",
                            city, JSON.toJSONString(areas), storeNo, e.getMessage(), e);
                }
            }
        }

        log.info("切仓任务ID:{} 查询到待履约订单数量:{}", waitOrderChangeHandleTask.getId(), allFulfillmentOrders.size());
        return allFulfillmentOrders;
    }

    /**
     * 过滤出城配仓有变更的订单数据
     */
    private List<FulfillmentOrderDTO> filterNeedChangeOrders(List<FulfillmentOrderDTO> fulfillmentOrders,
                                                           Map<String, AreaChangeInfo> areaChangeInfoMap) {
        if (CollectionUtils.isEmpty(fulfillmentOrders)) {
            return Collections.emptyList();
        }

        List<FulfillmentOrderDTO> needChangeOrders = new ArrayList<>();

        for (FulfillmentOrderDTO order : fulfillmentOrders) {
            // 检查订单的城市和区域是否为空
            String orderCity = order.getCity();
            String orderArea = order.getArea();

            if (orderCity == null || orderCity.trim().isEmpty()) {
                log.warn("履约订单城市为空，跳过处理，订单号:{}", order.getOuterOrderId());
                continue;
            }

            if (orderArea == null || orderArea.trim().isEmpty()) {
                log.warn("履约订单区域为空，跳过处理，订单号:{}", order.getOuterOrderId());
                continue;
            }

            String cityAreaKey = orderCity + "#" + orderArea;
            AreaChangeInfo changeInfo = areaChangeInfoMap.get(cityAreaKey);

            if (changeInfo != null && changeInfo.isStoreChanged()) {
                // 设置变更信息到订单中
                order.setOldStoreNo(changeInfo.getOldStoreNo());
                order.setNewStoreNo(changeInfo.getNewStoreNo());
                order.setOldAreaNo(changeInfo.getOldAreaNo());
                order.setNewAreaNo(changeInfo.getNewAreaNo());

                needChangeOrders.add(order);
            }
        }

        log.info("过滤出需要切仓的订单数量:{}", needChangeOrders.size());
        return needChangeOrders;
    }

    /**
     * 保存到切仓订单明细表
     */
    private List<FenceChangeTaskOrderEntity> saveTaskOrderDetails(List<FulfillmentOrderDTO> needChangeOrders, Long taskId) {
        if (CollectionUtils.isEmpty(needChangeOrders)) {
            return Collections.emptyList();
        }

        List<FenceChangeTaskOrderEntity> taskOrderEntities = new ArrayList<>();

        for (FulfillmentOrderDTO order : needChangeOrders) {
            try {
                FenceChangeTaskOrderEntity taskOrderEntity = FenceChangeTaskOrderConverter.dto2Entity(order);
                taskOrderEntity.create(taskId);
                taskOrderEntities.add(taskOrderEntity);
            } catch (Exception e) {
                log.error("转换订单数据异常，订单号:{}, 异常信息:{}", order.getOuterOrderId(), e.getMessage(), e);
            }
        }

        if (!CollectionUtils.isEmpty(taskOrderEntities)) {
            try {
                // 去重处理，避免重复插入
                Set<FenceChangeTaskOrderEntity> uniqueEntities = new LinkedHashSet<>(taskOrderEntities);
                fenceChangeTaskDetailRepository.saveBatch(uniqueEntities);
                log.info("保存切仓订单明细成功，任务ID:{}, 订单数量:{}", taskId, uniqueEntities.size());
                return new ArrayList<>(uniqueEntities);
            } catch (DuplicateKeyException e) {
                log.warn("保存切仓订单明细存在重复数据，任务ID:{}, 异常信息:{}", taskId, e.getMessage());
                return taskOrderEntities;
            } catch (Exception e) {
                log.error("保存切仓订单明细异常，任务ID:{}, 异常信息:{}", taskId, e.getMessage(), e);
                throw e;
            }
        }

        return taskOrderEntities;
    }

    /**
     * 区域变更信息
     */
    @Data
    private static class AreaChangeInfo {
        private String city;
        private String area;
        private Integer oldStoreNo;
        private Integer newStoreNo;
        private Integer oldAreaNo;
        private Integer newAreaNo;
        private boolean storeChanged;
        private boolean areaChanged;
    }

}
