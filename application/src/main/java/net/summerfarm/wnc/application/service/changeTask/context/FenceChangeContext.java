package net.summerfarm.wnc.application.service.changeTask.context;

import lombok.Builder;
import lombok.Data;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;

import java.util.List;

/**
     * 围栏变更上下文
     */
@Data
@Builder
public class FenceChangeContext {
    private String changeBatchNo;

    private List<WncCityAreaChangeWarehouseRecordsEntity> records;

    private String province;

    private String city;

    private String area;

    private List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords;

    private List<WncFenceChangeRecordsEntity> afterFenceChangeRecords;
}