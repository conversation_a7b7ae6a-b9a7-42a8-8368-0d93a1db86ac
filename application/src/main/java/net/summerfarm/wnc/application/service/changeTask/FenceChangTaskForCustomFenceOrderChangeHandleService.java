package net.summerfarm.wnc.application.service.changeTask;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义区域切订单服务
 * date: 2025/9/5 16:21<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForCustomFenceOrderChangeHandleService {

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    /**
     * 自定义区域切订单处理
     */
    public void customFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }

        // 1.根据切仓任务ID查询切仓变更批次
        List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities = wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(waitOrderChangeHandleTask.getId());

        // 2.分析变更类型和获取变更的城市区域信息
        FenceChangeAnalysis changeAnalysis = analyzeCustomFenceChange(fenceChangeRecordsEntities);
        if (changeAnalysis.getCustomAreaInfos().isEmpty()) {
            log.info("自定义围栏切仓任务ID:{} 无区域变更，直接完结任务", waitOrderChangeHandleTask.getId());
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));
            return;
        }

        // 3.根据变更类型和城市区域查询OFC订单，获取订单城市区域POI信息
        List<FulfillmentOrderDTO> fulfillmentOrders = queryOrdersByChangeType(changeAnalysis, waitOrderChangeHandleTask);

        // 4.把订单数据存到表中
        List<FenceChangeTaskOrderEntity> savedOrderEntities = saveOrdersToTable(fulfillmentOrders, waitOrderChangeHandleTask.getId());

        // 5.根据表中数据，通过城市、区域、POI进行快照前后的匹配查询
        List<FenceChangeTaskOrderEntity> matchedOrderEntities = matchOrdersWithSnapshot(savedOrderEntities, changeAnalysis);

        // 6.更新表中的匹配结果
        updateOrdersWithMatchResult(matchedOrderEntities);

        // 7.进行切仓处理
        processFenceChange(matchedOrderEntities, waitOrderChangeHandleTask);
    }

    /**
     * 分析自定义围栏变更类型和获取变更信息
     */
    private FenceChangeAnalysis analyzeCustomFenceChange(List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities) {
        FenceChangeAnalysis analysis = new FenceChangeAnalysis();
        List<CustomAreaInfo> customAreaInfos = new ArrayList<>();
        Set<String> processedCityAreas = new HashSet<>();

        // 分析变更前后的围栏类型
        Map<Integer, WncFenceChangeRecordsEntity> beforeRecordsMap = new HashMap<>();
        Map<Integer, WncFenceChangeRecordsEntity> afterRecordsMap = new HashMap<>();

        for (WncFenceChangeRecordsEntity record : fenceChangeRecordsEntities) {
            if (Objects.equals(record.getFenceChangeStage(), WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue())) {
                beforeRecordsMap.put(record.getFenceId(), record);
            } else if (Objects.equals(record.getFenceChangeStage(), WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue())) {
                afterRecordsMap.put(record.getFenceId(), record);
            }
        }

        // 提取区域信息并判断变更类型
        for (WncFenceChangeRecordsEntity record : fenceChangeRecordsEntities) {
            if (!CollectionUtils.isEmpty(record.getAreaChangeRecords())) {
                for (WncFenceAreaChangeRecordsEntity areaRecord : record.getAreaChangeRecords()) {
                    String city = areaRecord.getCity();
                    String area = areaRecord.getArea();
                    String customAreaName = areaRecord.getCustomAreaName();

                    if (city == null || city.trim().isEmpty()) {
                        log.warn("自定义区域变更记录城市为空，跳过处理，记录ID:{}", areaRecord.getId());
                        continue;
                    }

                    // 优先使用自定义区域名称
                    String finalAreaName = (customAreaName != null && !customAreaName.trim().isEmpty()) ? customAreaName : area;
                    if (finalAreaName == null || finalAreaName.trim().isEmpty()) {
                        log.warn("自定义区域变更记录区域和自定义区域名称都为空，跳过处理，记录ID:{}", areaRecord.getId());
                        continue;
                    }

                    String cityAreaKey = city + "#" + finalAreaName;
                    if (!processedCityAreas.contains(cityAreaKey)) {
                        CustomAreaInfo areaInfo = new CustomAreaInfo();
                        areaInfo.setCity(city);
                        areaInfo.setArea(finalAreaName);
                        areaInfo.setOriginalArea(area);
                        areaInfo.setCustomAreaName(customAreaName);

                        // 判断变更类型
                        CustomFenceChangeType changeType = determineChangeType(areaRecord.getFenceId(), beforeRecordsMap, afterRecordsMap);
                        areaInfo.setChangeType(changeType);

                        customAreaInfos.add(areaInfo);
                        processedCityAreas.add(cityAreaKey);

                        log.debug("提取自定义区域信息，城市:{}, 区域:{}, 自定义区域名称:{}, 变更类型:{}",
                                city, finalAreaName, customAreaName, changeType);
                    }
                }
            }
        }

        analysis.setCustomAreaInfos(customAreaInfos);
        analysis.setBeforeRecordsMap(beforeRecordsMap);
        analysis.setAfterRecordsMap(afterRecordsMap);

        log.info("分析到{}个自定义区域信息", customAreaInfos.size());
        return analysis;
    }

    /**
     * 判断自定义围栏变更类型
     */
    private CustomFenceChangeType determineChangeType(Integer fenceId,
                                                     Map<Integer, WncFenceChangeRecordsEntity> beforeRecordsMap,
                                                     Map<Integer, WncFenceChangeRecordsEntity> afterRecordsMap) {
        WncFenceChangeRecordsEntity beforeRecord = beforeRecordsMap.get(fenceId);
        WncFenceChangeRecordsEntity afterRecord = afterRecordsMap.get(fenceId);

        // 判断变更前的围栏类型
        boolean beforeIsCustom = beforeRecord != null && isCustomFence(beforeRecord);
        boolean beforeExists = beforeRecord != null;

        // 变更后都是自定义围栏
        if (!beforeExists) {
            return CustomFenceChangeType.NONE_TO_CUSTOM;
        } else if (beforeIsCustom) {
            return CustomFenceChangeType.CUSTOM_TO_CUSTOM;
        } else {
            return CustomFenceChangeType.NORMAL_TO_CUSTOM;
        }
    }

    /**
     * 判断是否为自定义围栏
     */
    private boolean isCustomFence(WncFenceChangeRecordsEntity record) {
        // 这里需要根据实际的业务逻辑判断是否为自定义围栏
        // 可能通过围栏类型字段或其他标识来判断
        // TODO: 需要根据实际的围栏类型字段来实现
        return record.getFenceName() != null && record.getFenceName().contains("自定义");
    }

    /**
     * 根据变更类型和城市区域查询OFC订单，获取订单城市区域POI信息
     */
    private List<FulfillmentOrderDTO> queryOrdersByChangeType(FenceChangeAnalysis changeAnalysis,
                                                             FenceChangeTaskEntity waitOrderChangeHandleTask) {
        List<FulfillmentOrderDTO> allFulfillmentOrders = new ArrayList<>();

        // 按变更类型分组处理
        Map<CustomFenceChangeType, List<CustomAreaInfo>> changeTypeMap = changeAnalysis.getCustomAreaInfos().stream()
                .collect(Collectors.groupingBy(CustomAreaInfo::getChangeType));

        for (Map.Entry<CustomFenceChangeType, List<CustomAreaInfo>> entry : changeTypeMap.entrySet()) {
            CustomFenceChangeType changeType = entry.getKey();
            List<CustomAreaInfo> areaInfos = entry.getValue();

            log.info("处理变更类型:{}, 区域数量:{}", changeType, areaInfos.size());

            List<FulfillmentOrderDTO> orders = queryOrdersBySpecificChangeType(changeType, areaInfos, changeAnalysis, waitOrderChangeHandleTask);
            allFulfillmentOrders.addAll(orders);
        }

        log.info("自定义围栏切仓任务ID:{} 查询到待履约订单数量:{}", waitOrderChangeHandleTask.getId(), allFulfillmentOrders.size());
        return allFulfillmentOrders;
    }

    /**
     * 根据具体的变更类型查询订单
     */
    private List<FulfillmentOrderDTO> queryOrdersBySpecificChangeType(CustomFenceChangeType changeType,
                                                                     List<CustomAreaInfo> areaInfos,
                                                                     FenceChangeAnalysis changeAnalysis,
                                                                     FenceChangeTaskEntity waitOrderChangeHandleTask) {
        List<FulfillmentOrderDTO> orders = new ArrayList<>();

        // 按城市分组查询
        Map<String, List<String>> cityAreasMap = areaInfos.stream()
                .collect(Collectors.groupingBy(
                        CustomAreaInfo::getCity,
                        Collectors.mapping(CustomAreaInfo::getArea, Collectors.toList())
                ));

        for (Map.Entry<String, List<String>> entry : cityAreasMap.entrySet()) {
            String city = entry.getKey();
            List<String> areas = entry.getValue();

            FulfillmentQueryInput queryInput = buildQueryInputByChangeType(changeType, city, areas, areaInfos, changeAnalysis, waitOrderChangeHandleTask);

            try {
                List<FulfillmentOrderDTO> fulfillmentOrders = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(queryInput);
                if (!CollectionUtils.isEmpty(fulfillmentOrders)) {
                    orders.addAll(fulfillmentOrders);
                    log.debug("变更类型:{}, 城市:{}，区域:{}的订单数量:{}", changeType, city, JSON.toJSONString(areas), fulfillmentOrders.size());
                }
            } catch (Exception e) {
                log.error("查询自定义围栏履约单数据异常，变更类型:{}, 城市:{}, 区域:{}, 异常信息:{}",
                        changeType, city, JSON.toJSONString(areas), e.getMessage(), e);
            }
        }

        return orders;
    }

    /**
     * 根据变更类型构建查询参数
     */
    private FulfillmentQueryInput buildQueryInputByChangeType(CustomFenceChangeType changeType,
                                                             String city,
                                                             List<String> areas,
                                                             List<CustomAreaInfo> areaInfos,
                                                             FenceChangeAnalysis changeAnalysis,
                                                             FenceChangeTaskEntity waitOrderChangeHandleTask) {
        FulfillmentQueryInput.FulfillmentQueryInputBuilder builder = FulfillmentQueryInput.builder()
                .city(city)
                .areas(areas)
                .deliveryDateBegin(waitOrderChangeHandleTask.getExeTimePlus2Date());

        switch (changeType) {
            case CUSTOM_TO_CUSTOM:
                // 自定义围栏到自定义围栏：查询原自定义围栏的订单
                Integer oldStoreNo = getOldStoreNoFromChangeAnalysis(city, areas, changeAnalysis);
                if (oldStoreNo != null) {
                    builder.storeNo(oldStoreNo);
                }
                break;
            case NORMAL_TO_CUSTOM:
                // 普通围栏到自定义围栏：查询原普通围栏的订单
                Integer normalStoreNo = getOldStoreNoFromChangeAnalysis(city, areas, changeAnalysis);
                if (normalStoreNo != null) {
                    builder.storeNo(normalStoreNo);
                }
                break;
            case NONE_TO_CUSTOM:
                // 无围栏到自定义围栏：查询该区域所有订单（不指定城配仓）
                // 不设置storeNo，查询所有订单
                break;
        }

        return builder.build();
    }

    /**
     * 从变更分析中获取变更前的城配仓编号
     */
    private Integer getOldStoreNoFromChangeAnalysis(String city, List<String> areas, FenceChangeAnalysis changeAnalysis) {
        // 根据城市区域找到对应的变更前记录
        return changeAnalysis.getBeforeRecordsMap().values().stream()
                .filter(record -> !CollectionUtils.isEmpty(record.getAreaChangeRecords()))
                .filter(record -> record.getAreaChangeRecords().stream()
                        .anyMatch(areaRecord -> city.equals(areaRecord.getCity()) &&
                                 areas.contains(areaRecord.getArea() != null ? areaRecord.getArea() : areaRecord.getCustomAreaName())))
                .map(WncFenceChangeRecordsEntity::getFenceStoreNo)
                .findFirst()
                .orElse(null);
    }

    /**
     * 把订单数据存到表中
     */
    private List<FenceChangeTaskOrderEntity> saveOrdersToTable(List<FulfillmentOrderDTO> fulfillmentOrders, Long taskId) {
        if (CollectionUtils.isEmpty(fulfillmentOrders)) {
            return Collections.emptyList();
        }

        List<FenceChangeTaskOrderEntity> taskOrderEntities = new ArrayList<>();

        for (FulfillmentOrderDTO order : fulfillmentOrders) {
            try {
                FenceChangeTaskOrderEntity taskOrderEntity = FenceChangeTaskOrderConverter.dto2Entity(order);
                taskOrderEntity.create(taskId);
                // 初始状态下，变更前后信息暂时为空，等待后续匹配
                taskOrderEntities.add(taskOrderEntity);
            } catch (Exception e) {
                log.error("转换自定义围栏订单数据异常，订单号:{}, 异常信息:{}", order.getOuterOrderId(), e.getMessage(), e);
            }
        }

        if (!CollectionUtils.isEmpty(taskOrderEntities)) {
            try {
                // 去重处理，避免重复插入
                Set<FenceChangeTaskOrderEntity> uniqueEntities = new LinkedHashSet<>(taskOrderEntities);
                fenceChangeTaskDetailRepository.saveBatch(uniqueEntities);
                log.info("保存自定义围栏订单到表成功，任务ID:{}, 订单数量:{}", taskId, uniqueEntities.size());
                return new ArrayList<>(uniqueEntities);
            } catch (DuplicateKeyException e) {
                log.warn("保存自定义围栏订单存在重复数据，任务ID:{}, 异常信息:{}", taskId, e.getMessage());
                return taskOrderEntities;
            } catch (Exception e) {
                log.error("保存自定义围栏订单异常，任务ID:{}, 异常信息:{}", taskId, e.getMessage(), e);
                throw e;
            }
        }

        return taskOrderEntities;
    }

    /**
     * 根据表中数据，通过城市、区域、POI进行快照前后的匹配查询
     */
    private List<FenceChangeTaskOrderEntity> matchOrdersWithSnapshot(List<FenceChangeTaskOrderEntity> savedOrderEntities,
                                                                    FenceChangeAnalysis changeAnalysis) {
        if (CollectionUtils.isEmpty(savedOrderEntities)) {
            return Collections.emptyList();
        }

        // 构建变更前后的快照映射
        Map<String, SnapshotInfo> beforeSnapshotMap = new HashMap<>();
        Map<String, SnapshotInfo> afterSnapshotMap = new HashMap<>();

        // 从变更分析中构建快照映射
        buildSnapshotMaps(changeAnalysis, beforeSnapshotMap, afterSnapshotMap);

        // 匹配订单与快照
        List<FenceChangeTaskOrderEntity> matchedOrders = new ArrayList<>();
        for (FenceChangeTaskOrderEntity orderEntity : savedOrderEntities) {
            String orderCity = orderEntity.getCity();
            String orderArea = orderEntity.getArea();

            if (orderCity == null || orderArea == null) {
                log.warn("订单城市或区域为空，跳过匹配，订单号:{}", orderEntity.getOuterOrderId());
                continue;
            }

            String orderCityAreaKey = orderCity + "#" + orderArea;
            SnapshotInfo beforeSnapshot = beforeSnapshotMap.get(orderCityAreaKey);
            SnapshotInfo afterSnapshot = afterSnapshotMap.get(orderCityAreaKey);

            // 根据不同的变更类型进行匹配
            CustomFenceChangeType changeType = determineOrderChangeType(orderCityAreaKey, changeAnalysis);
            boolean needChange = false;

            switch (changeType) {
                case CUSTOM_TO_CUSTOM:
                    needChange = matchCustomToCustom(orderEntity, beforeSnapshot, afterSnapshot);
                    break;
                case NORMAL_TO_CUSTOM:
                    needChange = matchNormalToCustom(orderEntity, beforeSnapshot, afterSnapshot);
                    break;
                case NONE_TO_CUSTOM:
                    needChange = matchNoneToCustom(orderEntity, afterSnapshot);
                    break;
            }

            if (needChange) {
                matchedOrders.add(orderEntity);
                log.debug("订单{}匹配成功，变更类型:{}, 需要切仓，城配仓:{}->{}, 运营区域:{}->{}",
                        orderEntity.getOuterOrderId(), changeType,
                        orderEntity.getOldStoreNo(), orderEntity.getNewStoreNo(),
                        orderEntity.getOldAreaNo(), orderEntity.getNewAreaNo());
            }
        }

        log.info("匹配到需要切仓的订单数量:{}", matchedOrders.size());
        return matchedOrders;
    }

    /**
     * 构建快照映射
     */
    private void buildSnapshotMaps(FenceChangeAnalysis changeAnalysis,
                                  Map<String, SnapshotInfo> beforeSnapshotMap,
                                  Map<String, SnapshotInfo> afterSnapshotMap) {
        // 构建变更前快照
        for (WncFenceChangeRecordsEntity record : changeAnalysis.getBeforeRecordsMap().values()) {
            buildSnapshotFromRecord(record, beforeSnapshotMap, true);
        }

        // 构建变更后快照
        for (WncFenceChangeRecordsEntity record : changeAnalysis.getAfterRecordsMap().values()) {
            buildSnapshotFromRecord(record, afterSnapshotMap, false);
        }
    }

    /**
     * 从记录构建快照
     */
    private void buildSnapshotFromRecord(WncFenceChangeRecordsEntity record,
                                        Map<String, SnapshotInfo> snapshotMap,
                                        boolean isBefore) {
        if (!CollectionUtils.isEmpty(record.getAreaChangeRecords())) {
            for (WncFenceAreaChangeRecordsEntity areaRecord : record.getAreaChangeRecords()) {
                String city = areaRecord.getCity();
                String area = areaRecord.getArea();
                String customAreaName = areaRecord.getCustomAreaName();

                // 优先使用自定义区域名称
                String finalAreaName = (customAreaName != null && !customAreaName.trim().isEmpty()) ? customAreaName : area;
                if (city == null || finalAreaName == null) {
                    continue;
                }

                String cityAreaKey = city + "#" + finalAreaName;

                SnapshotInfo snapshot = new SnapshotInfo();
                snapshot.setFenceId(record.getFenceId());
                snapshot.setStoreNo(record.getFenceStoreNo());
                snapshot.setAreaNo(record.getFenceAreaNo());
                snapshot.setCity(city);
                snapshot.setArea(finalAreaName);
                snapshot.setBefore(isBefore);

                snapshotMap.put(cityAreaKey, snapshot);
            }
        }
    }

    /**
     * 确定订单的变更类型
     */
    private CustomFenceChangeType determineOrderChangeType(String orderCityAreaKey, FenceChangeAnalysis changeAnalysis) {
        return changeAnalysis.getCustomAreaInfos().stream()
                .filter(info -> orderCityAreaKey.equals(info.getCity() + "#" + info.getArea()))
                .map(CustomAreaInfo::getChangeType)
                .findFirst()
                .orElse(CustomFenceChangeType.CUSTOM_TO_CUSTOM); // 默认为自定义到自定义
    }

    /**
     * 匹配自定义围栏到自定义围栏的变更
     */
    private boolean matchCustomToCustom(FenceChangeTaskOrderEntity orderEntity,
                                       SnapshotInfo beforeSnapshot,
                                       SnapshotInfo afterSnapshot) {
        if (beforeSnapshot == null || afterSnapshot == null) {
            log.warn("自定义围栏到自定义围栏变更，快照信息不完整，订单号:{}", orderEntity.getOuterOrderId());
            return false;
        }

        // 检查是否需要切仓
        boolean needChange = !Objects.equals(beforeSnapshot.getStoreNo(), afterSnapshot.getStoreNo()) ||
                           !Objects.equals(beforeSnapshot.getAreaNo(), afterSnapshot.getAreaNo());

        if (needChange) {
            // 设置变更前后信息
            orderEntity.setOldFenceId(beforeSnapshot.getFenceId());
            orderEntity.setNewFenceId(afterSnapshot.getFenceId());
            orderEntity.setOldStoreNo(beforeSnapshot.getStoreNo());
            orderEntity.setNewStoreNo(afterSnapshot.getStoreNo());
            orderEntity.setOldAreaNo(beforeSnapshot.getAreaNo());
            orderEntity.setNewAreaNo(afterSnapshot.getAreaNo());
        }

        return needChange;
    }

    /**
     * 匹配普通围栏到自定义围栏的变更
     */
    private boolean matchNormalToCustom(FenceChangeTaskOrderEntity orderEntity,
                                       SnapshotInfo beforeSnapshot,
                                       SnapshotInfo afterSnapshot) {
        if (beforeSnapshot == null || afterSnapshot == null) {
            log.warn("普通围栏到自定义围栏变更，快照信息不完整，订单号:{}", orderEntity.getOuterOrderId());
            return false;
        }

        // 从普通围栏切换到自定义围栏，通常都需要切仓
        orderEntity.setOldFenceId(beforeSnapshot.getFenceId());
        orderEntity.setNewFenceId(afterSnapshot.getFenceId());
        orderEntity.setOldStoreNo(beforeSnapshot.getStoreNo());
        orderEntity.setNewStoreNo(afterSnapshot.getStoreNo());
        orderEntity.setOldAreaNo(beforeSnapshot.getAreaNo());
        orderEntity.setNewAreaNo(afterSnapshot.getAreaNo());

        return true; // 普通围栏到自定义围栏通常都需要切仓
    }

    /**
     * 匹配无围栏到自定义围栏的变更
     */
    private boolean matchNoneToCustom(FenceChangeTaskOrderEntity orderEntity,
                                     SnapshotInfo afterSnapshot) {
        if (afterSnapshot == null) {
            log.warn("无围栏到自定义围栏变更，变更后快照信息不存在，订单号:{}", orderEntity.getOuterOrderId());
            return false;
        }

        // 从无围栏状态切换到自定义围栏
        orderEntity.setOldFenceId(null); // 原来无围栏
        orderEntity.setNewFenceId(afterSnapshot.getFenceId());
        orderEntity.setOldStoreNo(null); // 原来无城配仓
        orderEntity.setNewStoreNo(afterSnapshot.getStoreNo());
        orderEntity.setOldAreaNo(null); // 原来无运营区域
        orderEntity.setNewAreaNo(afterSnapshot.getAreaNo());

        return true; // 无围栏到自定义围栏都需要切仓
    }

    /**
     * 更新表中的匹配结果
     */
    private void updateOrdersWithMatchResult(List<FenceChangeTaskOrderEntity> matchedOrderEntities) {
        if (CollectionUtils.isEmpty(matchedOrderEntities)) {
            return;
        }

        try {
            // 逐个更新订单的变更前后信息
            for (FenceChangeTaskOrderEntity orderEntity : matchedOrderEntities) {
                fenceChangeTaskDetailRepository.update(orderEntity);
            }
            log.info("更新订单匹配结果成功，订单数量:{}", matchedOrderEntities.size());
        } catch (Exception e) {
            log.error("更新订单匹配结果异常，异常信息:{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 进行切仓处理
     */
    private void processFenceChange(List<FenceChangeTaskOrderEntity> matchedOrderEntities,
                                   FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (CollectionUtils.isEmpty(matchedOrderEntities)) {
            log.info("自定义围栏切仓任务ID:{} 无需要切仓的订单，直接完结任务", waitOrderChangeHandleTask.getId());
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));
        } else {
            log.info("自定义围栏切仓任务ID:{} 需要切仓的订单数量:{}", waitOrderChangeHandleTask.getId(), matchedOrderEntities.size());

            // 更新任务状态为订单切换中
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.ORDER_CHANGE_ING));

            // TODO: 调用OFC进行切仓 - 需要OFC做支持
            // 这里可以调用OFC的切仓接口，传入需要切仓的订单列表
            log.info("开始调用OFC进行自定义围栏切仓处理，订单数量:{}", matchedOrderEntities.size());
        }
    }



    /**
     * 自定义围栏变更类型枚举
     */
    private enum CustomFenceChangeType {
        /** 自定义围栏 -> 自定义围栏 */
        CUSTOM_TO_CUSTOM,
        /** 普通围栏 -> 自定义围栏 */
        NORMAL_TO_CUSTOM,
        /** 无围栏 -> 自定义围栏 */
        NONE_TO_CUSTOM
    }

    /**
     * 自定义区域信息
     */
    @Data
    private static class CustomAreaInfo {
        private String city;
        private String area;
        private String originalArea;
        private String customAreaName;
        private CustomFenceChangeType changeType;
    }

    /**
     * 快照信息
     */
    @Data
    private static class SnapshotInfo {
        private Integer fenceId;
        private Integer storeNo;
        private Integer areaNo;
        private String city;
        private String area;
        private boolean isBefore; // 是否为变更前快照
    }

    /**
     * 围栏变更分析结果
     */
    @Data
    private static class FenceChangeAnalysis {
        private List<CustomAreaInfo> customAreaInfos;
        private Map<Integer, WncFenceChangeRecordsEntity> beforeRecordsMap;
        private Map<Integer, WncFenceChangeRecordsEntity> afterRecordsMap;
    }

}
