package net.summerfarm.wnc.application.service.changeTask;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.context.FenceChangeContext;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.application.service.changeTask.factory.FenceChangeTaskContextFactory;
import net.summerfarm.wnc.common.enums.*;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.AdCodeMsgRepository;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.WncFenceChangeRecordsQueryDomainService;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义区域切订单服务
 * date: 2025/9/5 16:21<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForCustomFenceOrderChangeHandleService {

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Resource
    private FenceChangeTaskContextFactory fenceChangeTaskContextFactory;
    @Resource
    private WncFenceChangeRecordsQueryDomainService wncFenceChangeRecordsQueryDomainService;
    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;
    private AdCodeMsgRepository adCodeMsgRepository;
    @Autowired
    private FenceRepository fenceRepository;

    /**
     * 自定义区域切订单处理
     */
    public void customFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }

        try {
            log.info("开始处理自定义围栏切仓订单，任务ID:{}", waitOrderChangeHandleTask.getId());

            // 1.构建围栏变更上下文
            FenceChangeContext context = buildFenceChangeContext(waitOrderChangeHandleTask);
            if (context == null) {
                return;
            }

            // 2.判断围栏变更类型并处理
            FenceChangeTypeEnum changeType = determineChangeType(context);
            processOrdersByChangeType(waitOrderChangeHandleTask, context, changeType);

            log.info("完成处理自定义围栏切仓订单，任务ID:{}", waitOrderChangeHandleTask.getId());
        } catch (Exception e) {
            log.error("自定义围栏切仓订单处理异常，任务ID:{}, 异常信息:{}", waitOrderChangeHandleTask.getId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 构建围栏变更上下文
     */
    private FenceChangeContext buildFenceChangeContext(FenceChangeTaskEntity task) {
        // 查询围栏变更记录
        List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities =
            wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(task.getId());

        if (CollectionUtils.isEmpty(fenceChangeRecordsEntities)) {
            log.info("围栏变更记录为空，跳过处理，任务ID:{}", task.getId());
            return null;
        }

        // 构建上下文
        String changeBatchNo = fenceChangeRecordsEntities.get(0).getChangeBatchNo();
        FenceChangeContext context = fenceChangeTaskContextFactory.buildFenceChangeContext(changeBatchNo, Collections.emptyList());

        if (context == null) {
            log.info("自定义围栏切仓订单任务ID:{} 无围栏变更上下文，直接完结任务", task.getId());
        }

        return context;
    }

    /**
     * 判断围栏变更类型
     */
    private FenceChangeTypeEnum determineChangeType(FenceChangeContext context) {
        return wncFenceChangeRecordsQueryDomainService.determineCustomFenceChangeType(context.getBeforeFenceChangeRecords());
    }

    /**
     * 根据变更类型处理订单
     */
    private void processOrdersByChangeType(FenceChangeTaskEntity task, FenceChangeContext context, FenceChangeTypeEnum changeType) {
        log.info("处理围栏变更类型:{}, 任务ID:{}", changeType, task.getId());

        switch (changeType) {
            case NONE_TO_CUSTOM:
                handleNoneToCustom(task);
                break;
            case CUSTOM_TO_CUSTOM:
            case NORMAL_TO_CUSTOM:
                handleFenceChange(task, context, changeType);
                break;
            default:
                log.error("未知的围栏变更类型:{}, 任务ID:{}", changeType, task.getId());
                break;
        }
    }

    /**
     * 处理无围栏到自定义围栏的情况
     */
    private void handleNoneToCustom(FenceChangeTaskEntity task) {
        log.info("无围栏到自定义围栏，直接完结任务，任务ID:{}", task.getId());
        fenceChangeTaskRepository.update(task.execute(FenceChangeTaskEnums.Status.COMPLETED));
    }

    /**
     * 处理围栏变更（自定义到自定义、普通到自定义）
     */
    private void handleFenceChange(FenceChangeTaskEntity task, FenceChangeContext context, FenceChangeTypeEnum changeType) {
        // 1.获取变更前的城配仓编号
        List<Integer> beforeStoreNos = getBeforeStoreNos(context, changeType);
        if (CollectionUtils.isEmpty(beforeStoreNos)) {
            log.warn("未获取到变更前的城配仓编号，任务ID:{}", task.getId());
            return;
        }

        // 2.查询OFC订单
        List<FulfillmentOrderDTO> fulfillmentOrders = queryFulfillmentOrders(task, context, beforeStoreNos);
        if (CollectionUtils.isEmpty(fulfillmentOrders)) {
            log.info("未查询到待处理订单，任务ID:{}", task.getId());
            return;
        }

        // 3.保存订单到表中
        List<FenceChangeTaskOrderEntity> orderEntities = saveOrdersToTable(fulfillmentOrders);

        // 4.匹配订单与围栏信息
        matchOrdersWithFenceInfo(orderEntities, context, changeType);
    }

    /**
     * 获取变更前的城配仓编号
     */
    private List<Integer> getBeforeStoreNos(FenceChangeContext context, FenceChangeTypeEnum changeType) {
        List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords = context.getBeforeFenceChangeRecords();
        String city = context.getCity();
        String area = context.getArea();

        if (FenceChangeTypeEnum.CUSTOM_TO_CUSTOM.equals(changeType)) {
            return beforeFenceChangeRecords.stream()
                    .map(WncFenceChangeRecordsEntity::getFenceStoreNo)
                    .distinct()
                    .collect(Collectors.toList());
        } else if (FenceChangeTypeEnum.NORMAL_TO_CUSTOM.equals(changeType)) {
            List<WncFenceAreaChangeRecordsEntity> fenceAreaChangeRecords = context.beforeAreaChangeRecordsGet();
            List<WncFenceAreaChangeRecordsEntity> matchedAreaChangeRecords = fenceAreaChangeRecords.stream()
                    .filter(e -> Objects.equals(e.getCity(), city) && Objects.equals(e.getArea(), area))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(matchedAreaChangeRecords)) {
                return Collections.emptyList();
            }

            Integer fenceId = matchedAreaChangeRecords.get(0).getFenceId();
            return beforeFenceChangeRecords.stream()
                    .filter(e -> Objects.equals(e.getFenceId(), fenceId))
                    .map(WncFenceChangeRecordsEntity::getFenceStoreNo)
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    /**
     * 查询履约订单
     */
    private List<FulfillmentOrderDTO> queryFulfillmentOrders(FenceChangeTaskEntity task, FenceChangeContext context, List<Integer> beforeStoreNos) {
        List<FulfillmentOrderDTO> allFulfillmentOrders = new ArrayList<>();
        String city = context.getCity();
        String area = context.getArea();

        for (Integer storeNo : beforeStoreNos) {
            try {
                FulfillmentQueryInput queryInput = FulfillmentQueryInput.builder()
                        .city(city)
                        .areas(Collections.singletonList(area))
                        .storeNo(storeNo)
                        .deliveryDateBegin(task.getExeTimePlus2Date())
                        .build();

                List<FulfillmentOrderDTO> fulfillmentOrders = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(queryInput);
                if (!CollectionUtils.isEmpty(fulfillmentOrders)) {
                    allFulfillmentOrders.addAll(fulfillmentOrders);
                    log.debug("查询到订单数量:{}, 城配仓:{}, 城市:{}, 区域:{}", fulfillmentOrders.size(), storeNo, city, area);
                }
            } catch (Exception e) {
                log.error("查询履约订单异常，城配仓:{}, 城市:{}, 区域:{}, 异常信息:{}", storeNo, city, area, e.getMessage(), e);
            }
        }

        log.info("总共查询到订单数量:{}, 任务ID:{}", allFulfillmentOrders.size(), task.getId());
        return allFulfillmentOrders;
    }

    /**
     * 保存订单到表中
     */
    private List<FenceChangeTaskOrderEntity> saveOrdersToTable(List<FulfillmentOrderDTO> fulfillmentOrders) {
        List<FenceChangeTaskOrderEntity> orderEntities = fulfillmentOrders.stream()
                .map(FenceChangeTaskOrderConverter::dto2Entity)
                .collect(Collectors.toList());

        fenceChangeTaskDomainService.saveBatchDetail(orderEntities);
        log.info("保存订单到表中，数量:{}", orderEntities.size());

        return orderEntities;
    }

        // 7.根据表中数据，通过城市、区域、POI进行快照前后的匹配查询
        saveFenceChangeTaskOrderList.forEach(orderInfo ->{
            String orderCity = orderInfo.getCity();
            String orderArea = orderInfo.getArea();
            String orderPoi = orderInfo.getPoi();

            if (FenceChangeTypeEnum.CUSTOM_TO_CUSTOM.equals(changeType)) {

                // Es POI匹配历史围栏区域
                List<Integer> beforeAdCodeMsgIds = beforeAreaChangeRecordsList.stream()
                        .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                        .map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId)
                        .collect(Collectors.toList());

               Integer beforeAdCodeMsgId = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(beforeAdCodeMsgIds, orderPoi);
               if (beforeAdCodeMsgId == null) {
                   fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), "未匹配到旧的自定义围栏");
                   return;
               }

               // Es POI匹配新围栏区域【有效配送区域】
                List<Integer> afterAdCodeMsgIds = afterAreaChangeRecordsList.stream()
                        .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                        .filter(e -> Objects.equals(e.getAdCodeMsgDetailEntity().getStatus(), AdCodeMsgEnums.Status.VALID.getValue()))
                        .map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId)
                        .collect(Collectors.toList());

                Integer afterAdCodeMsgId = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(afterAdCodeMsgIds, orderPoi);
                if (afterAdCodeMsgId == null) {
                    fenceChangeTaskDomainService.orderChangeNoNeedHandle(orderInfo.getId(), "不在有效配送区域");
                    return;
                }

                // 旧围栏信息
                Map<Integer, FenceEntity> beforeAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(afterAdCodeMsgId));
                FenceEntity beforeFenceEntity = beforeAdMsgIdTOFenceEntityMap.get(afterAdCodeMsgId);

                Integer beforeStoreNo = beforeFenceEntity.getStoreNo();
                Integer beforeFenceId = beforeFenceEntity.getId();
                Integer beforeAreaNo = beforeFenceEntity.getAreaNo();

                // 新围栏信息
                Map<Integer, FenceEntity> afterAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(afterAdCodeMsgId));
                FenceEntity afterFenceEntity = afterAdMsgIdTOFenceEntityMap.get(afterAdCodeMsgId);

                Integer afterStoreNo = afterFenceEntity.getStoreNo();
                Integer afterFenceId = afterFenceEntity.getId();
                Integer afterAreaNo = afterFenceEntity.getAreaNo();

                FenceChangeTaskOrderEntity oldNewFenceInfoEntity = new FenceChangeTaskOrderEntity();
                oldNewFenceInfoEntity.setId(orderInfo.getId());

                orderInfo.setOldAdCodeMsgId(beforeAdCodeMsgId);
                orderInfo.setOldFenceId(beforeFenceId);
                orderInfo.setOldStoreNo(beforeStoreNo);
                orderInfo.setOldAreaNo(beforeAreaNo);

                orderInfo.setNewAdCodeMsgId(afterAdCodeMsgId);
                orderInfo.setNewFenceId(afterFenceId);
                orderInfo.setNewStoreNo(afterStoreNo);
                orderInfo.setNewAreaNo(afterAreaNo);

                fenceChangeTaskDomainService.updateOldNewFenceInfo(oldNewFenceInfoEntity);

            } else {
                // 普通围栏 -> 自定义围栏

                // 匹配订单所属的旧围栏区域
                WncFenceAreaChangeRecordsEntity beforeMatchedAreaChangeRecord = beforeAreaChangeRecordsList.stream()
                        .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                        .findFirst()
                        .orElse(null);
                if(beforeMatchedAreaChangeRecord == null){
                    fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), "未匹配到旧的围栏");
                    return;
                }

                Integer beforeAdCodeMsgId = beforeMatchedAreaChangeRecord.getAdCodeMsgId();
                Integer beforeFenceId = beforeMatchedAreaChangeRecord.getFenceId();
                // 旧围栏信息
                FenceEntity beforeFenceEntity = fenceRepository.queryById(beforeFenceId);
                if (beforeFenceEntity == null) {
                    fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), "未匹配查询到旧的围栏");
                    return;
                }

                // 新围栏信息
                // Es POI匹配新围栏区域【有效配送区域】
                List<Integer> afterAdCodeMsgIds = afterAreaChangeRecordsList.stream()
                        .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                        .filter(e -> Objects.equals(e.getAdCodeMsgDetailEntity().getStatus(), AdCodeMsgEnums.Status.VALID.getValue()))
                        .map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId)
                        .collect(Collectors.toList());

                Integer afterAdCodeMsgId = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(afterAdCodeMsgIds, orderPoi);
                if (afterAdCodeMsgId == null) {
                    fenceChangeTaskDomainService.orderChangeNoNeedHandle(orderInfo.getId(), "不在有效配送区域");
                    return;
                }


                Integer beforeStoreNo = beforeFenceEntity.getStoreNo();
                Integer beforeAreaNo = beforeFenceEntity.getAreaNo();

                // 新围栏信息
                Map<Integer, FenceEntity> afterAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(afterAdCodeMsgId));
                FenceEntity afterFenceEntity = afterAdMsgIdTOFenceEntityMap.get(afterAdCodeMsgId);

                Integer afterStoreNo = afterFenceEntity.getStoreNo();
                Integer afterFenceId = afterFenceEntity.getId();
                Integer afterAreaNo = afterFenceEntity.getAreaNo();

                // 保存订单切围栏前后信息
                FenceChangeTaskOrderEntity oldNewFenceInfoEntity = new FenceChangeTaskOrderEntity();
                oldNewFenceInfoEntity.setId(orderInfo.getId());

                orderInfo.setOldAdCodeMsgId(beforeAdCodeMsgId);
                orderInfo.setOldFenceId(beforeFenceId);
                orderInfo.setOldStoreNo(beforeStoreNo);
                orderInfo.setOldAreaNo(beforeAreaNo);

                orderInfo.setNewAdCodeMsgId(afterAdCodeMsgId);
                orderInfo.setNewFenceId(afterFenceId);
                orderInfo.setNewStoreNo(afterStoreNo);
                orderInfo.setNewAreaNo(afterAreaNo);

                fenceChangeTaskDomainService.updateOldNewFenceInfo(oldNewFenceInfoEntity);
            }
        });

        // 9.进行切仓处理
    }







}
