package net.summerfarm.wnc.application.service.changeTask;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义区域切订单服务
 * date: 2025/9/5 16:21<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForCustomFenceOrderChangeHandleService {

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    /**
     * 自定义区域切订单处理
     */
    public void customFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }

        // 1.根据切仓任务ID查询切仓变更批次
        List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities = wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(waitOrderChangeHandleTask.getId());

        // 2.获取变更的城市区域信息
        List<CustomAreaInfo> customAreaInfos = extractCustomAreaInfos(fenceChangeRecordsEntities);
        if (customAreaInfos.isEmpty()) {
            log.info("自定义围栏切仓任务ID:{} 无区域变更，直接完结任务", waitOrderChangeHandleTask.getId());
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));
            return;
        }

        // 3.根据城市区域查询OFC订单，获取订单城市区域POI信息
        List<FulfillmentOrderDTO> fulfillmentOrders = queryOrdersByCityArea(customAreaInfos, waitOrderChangeHandleTask);

        // 4.把订单数据存到表中
        List<FenceChangeTaskOrderEntity> savedOrderEntities = saveOrdersToTable(fulfillmentOrders, waitOrderChangeHandleTask.getId());

        // 5.根据表中数据，通过城市、区域、POI进行快照前后的匹配查询
        List<FenceChangeTaskOrderEntity> matchedOrderEntities = matchOrdersWithSnapshot(savedOrderEntities, fenceChangeRecordsEntities);

        // 6.更新表中的匹配结果
        updateOrdersWithMatchResult(matchedOrderEntities);

        // 7.进行切仓处理
        processFenceChange(matchedOrderEntities, waitOrderChangeHandleTask);
    }

    /**
     * 提取自定义区域信息
     */
    private List<CustomAreaInfo> extractCustomAreaInfos(List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities) {
        List<CustomAreaInfo> customAreaInfos = new ArrayList<>();
        Set<String> processedCityAreas = new HashSet<>();

        for (WncFenceChangeRecordsEntity record : fenceChangeRecordsEntities) {
            if (!CollectionUtils.isEmpty(record.getAreaChangeRecords())) {
                for (WncFenceAreaChangeRecordsEntity areaRecord : record.getAreaChangeRecords()) {
                    String city = areaRecord.getCity();
                    String area = areaRecord.getArea();
                    String customAreaName = areaRecord.getCustomAreaName();

                    if (city == null || city.trim().isEmpty()) {
                        log.warn("自定义区域变更记录城市为空，跳过处理，记录ID:{}", areaRecord.getId());
                        continue;
                    }

                    // 优先使用自定义区域名称
                    String finalAreaName = (customAreaName != null && !customAreaName.trim().isEmpty()) ? customAreaName : area;
                    if (finalAreaName == null || finalAreaName.trim().isEmpty()) {
                        log.warn("自定义区域变更记录区域和自定义区域名称都为空，跳过处理，记录ID:{}", areaRecord.getId());
                        continue;
                    }

                    String cityAreaKey = city + "#" + finalAreaName;
                    if (!processedCityAreas.contains(cityAreaKey)) {
                        CustomAreaInfo areaInfo = new CustomAreaInfo();
                        areaInfo.setCity(city);
                        areaInfo.setArea(finalAreaName);
                        areaInfo.setOriginalArea(area);
                        areaInfo.setCustomAreaName(customAreaName);
                        customAreaInfos.add(areaInfo);
                        processedCityAreas.add(cityAreaKey);

                        log.debug("提取自定义区域信息，城市:{}, 区域:{}, 自定义区域名称:{}", city, finalAreaName, customAreaName);
                    }
                }
            }
        }

        log.info("提取到{}个自定义区域信息", customAreaInfos.size());
        return customAreaInfos;
    }

    /**
     * 根据城市区域查询OFC订单，获取订单城市区域POI信息
     */
    private List<FulfillmentOrderDTO> queryOrdersByCityArea(List<CustomAreaInfo> customAreaInfos,
                                                           FenceChangeTaskEntity waitOrderChangeHandleTask) {
        List<FulfillmentOrderDTO> allFulfillmentOrders = new ArrayList<>();

        // 按城市分组查询
        Map<String, List<String>> cityAreasMap = customAreaInfos.stream()
                .collect(Collectors.groupingBy(
                        CustomAreaInfo::getCity,
                        Collectors.mapping(CustomAreaInfo::getArea, Collectors.toList())
                ));

        for (Map.Entry<String, List<String>> entry : cityAreasMap.entrySet()) {
            String city = entry.getKey();
            List<String> areas = entry.getValue();

            // 自定义围栏查询时不指定具体的城配仓，让OFC返回该城市区域的所有订单
            FulfillmentQueryInput queryInput = FulfillmentQueryInput.builder()
                    .city(city)
                    .areas(areas)
                    .deliveryDateBegin(waitOrderChangeHandleTask.getExeTimePlus2Date()) // T+2的时间点
                    .build();

            try {
                List<FulfillmentOrderDTO> fulfillmentOrders = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(queryInput);
                if (!CollectionUtils.isEmpty(fulfillmentOrders)) {
                    allFulfillmentOrders.addAll(fulfillmentOrders);
                    log.debug("查询到城市:{}，区域:{}的订单数量:{}", city, JSON.toJSONString(areas), fulfillmentOrders.size());
                }
            } catch (Exception e) {
                log.error("查询自定义围栏履约单数据异常，城市:{}, 区域:{}, 异常信息:{}",
                        city, JSON.toJSONString(areas), e.getMessage(), e);
            }
        }

        log.info("自定义围栏切仓任务ID:{} 查询到待履约订单数量:{}", waitOrderChangeHandleTask.getId(), allFulfillmentOrders.size());
        return allFulfillmentOrders;
    }

    /**
     * 把订单数据存到表中
     */
    private List<FenceChangeTaskOrderEntity> saveOrdersToTable(List<FulfillmentOrderDTO> fulfillmentOrders, Long taskId) {
        if (CollectionUtils.isEmpty(fulfillmentOrders)) {
            return Collections.emptyList();
        }

        List<FenceChangeTaskOrderEntity> taskOrderEntities = new ArrayList<>();

        for (FulfillmentOrderDTO order : fulfillmentOrders) {
            try {
                FenceChangeTaskOrderEntity taskOrderEntity = FenceChangeTaskOrderConverter.dto2Entity(order);
                taskOrderEntity.create(taskId);
                // 初始状态下，变更前后信息暂时为空，等待后续匹配
                taskOrderEntities.add(taskOrderEntity);
            } catch (Exception e) {
                log.error("转换自定义围栏订单数据异常，订单号:{}, 异常信息:{}", order.getOuterOrderId(), e.getMessage(), e);
            }
        }

        if (!CollectionUtils.isEmpty(taskOrderEntities)) {
            try {
                // 去重处理，避免重复插入
                Set<FenceChangeTaskOrderEntity> uniqueEntities = new LinkedHashSet<>(taskOrderEntities);
                fenceChangeTaskDetailRepository.saveBatch(uniqueEntities);
                log.info("保存自定义围栏订单到表成功，任务ID:{}, 订单数量:{}", taskId, uniqueEntities.size());
                return new ArrayList<>(uniqueEntities);
            } catch (DuplicateKeyException e) {
                log.warn("保存自定义围栏订单存在重复数据，任务ID:{}, 异常信息:{}", taskId, e.getMessage());
                return taskOrderEntities;
            } catch (Exception e) {
                log.error("保存自定义围栏订单异常，任务ID:{}, 异常信息:{}", taskId, e.getMessage(), e);
                throw e;
            }
        }

        return taskOrderEntities;
    }

    /**
     * 根据表中数据，通过城市、区域、POI进行快照前后的匹配查询
     */
    private List<FenceChangeTaskOrderEntity> matchOrdersWithSnapshot(List<FenceChangeTaskOrderEntity> savedOrderEntities,
                                                                    List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities) {
        if (CollectionUtils.isEmpty(savedOrderEntities)) {
            return Collections.emptyList();
        }

        // 构建变更前后的快照映射
        Map<String, SnapshotInfo> beforeSnapshotMap = new HashMap<>();
        Map<String, SnapshotInfo> afterSnapshotMap = new HashMap<>();

        for (WncFenceChangeRecordsEntity record : fenceChangeRecordsEntities) {
            if (!CollectionUtils.isEmpty(record.getAreaChangeRecords())) {
                for (WncFenceAreaChangeRecordsEntity areaRecord : record.getAreaChangeRecords()) {
                    String city = areaRecord.getCity();
                    String area = areaRecord.getArea();
                    String customAreaName = areaRecord.getCustomAreaName();

                    // 优先使用自定义区域名称
                    String finalAreaName = (customAreaName != null && !customAreaName.trim().isEmpty()) ? customAreaName : area;
                    if (city == null || finalAreaName == null) {
                        continue;
                    }

                    String cityAreaKey = city + "#" + finalAreaName;

                    if (Objects.equals(record.getFenceChangeStage(), WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue())) {
                        // 变更前快照
                        SnapshotInfo beforeSnapshot = new SnapshotInfo();
                        beforeSnapshot.setFenceId(record.getFenceId());
                        beforeSnapshot.setStoreNo(record.getFenceStoreNo());
                        beforeSnapshot.setAreaNo(record.getFenceAreaNo());
                        beforeSnapshot.setCity(city);
                        beforeSnapshot.setArea(finalAreaName);
                        beforeSnapshotMap.put(cityAreaKey, beforeSnapshot);
                    } else if (Objects.equals(record.getFenceChangeStage(), WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue())) {
                        // 变更后快照
                        SnapshotInfo afterSnapshot = new SnapshotInfo();
                        afterSnapshot.setFenceId(record.getFenceId());
                        afterSnapshot.setStoreNo(record.getFenceStoreNo());
                        afterSnapshot.setAreaNo(record.getFenceAreaNo());
                        afterSnapshot.setCity(city);
                        afterSnapshot.setArea(finalAreaName);
                        afterSnapshotMap.put(cityAreaKey, afterSnapshot);
                    }
                }
            }
        }

        // 匹配订单与快照
        List<FenceChangeTaskOrderEntity> matchedOrders = new ArrayList<>();
        for (FenceChangeTaskOrderEntity orderEntity : savedOrderEntities) {
            String orderCity = orderEntity.getCity();
            String orderArea = orderEntity.getArea();

            if (orderCity == null || orderArea == null) {
                log.warn("订单城市或区域为空，跳过匹配，订单号:{}", orderEntity.getOuterOrderId());
                continue;
            }

            String orderCityAreaKey = orderCity + "#" + orderArea;
            SnapshotInfo beforeSnapshot = beforeSnapshotMap.get(orderCityAreaKey);
            SnapshotInfo afterSnapshot = afterSnapshotMap.get(orderCityAreaKey);

            if (beforeSnapshot != null && afterSnapshot != null) {
                // 检查是否需要切仓
                boolean needChange = !Objects.equals(beforeSnapshot.getStoreNo(), afterSnapshot.getStoreNo()) ||
                                   !Objects.equals(beforeSnapshot.getAreaNo(), afterSnapshot.getAreaNo());

                if (needChange) {
                    // 设置变更前后信息
                    orderEntity.setOldFenceId(beforeSnapshot.getFenceId());
                    orderEntity.setNewFenceId(afterSnapshot.getFenceId());
                    orderEntity.setOldStoreNo(beforeSnapshot.getStoreNo());
                    orderEntity.setNewStoreNo(afterSnapshot.getStoreNo());
                    orderEntity.setOldAreaNo(beforeSnapshot.getAreaNo());
                    orderEntity.setNewAreaNo(afterSnapshot.getAreaNo());

                    matchedOrders.add(orderEntity);
                    log.debug("订单{}匹配成功，需要切仓，城配仓:{}->{}, 运营区域:{}->{}",
                            orderEntity.getOuterOrderId(),
                            beforeSnapshot.getStoreNo(), afterSnapshot.getStoreNo(),
                            beforeSnapshot.getAreaNo(), afterSnapshot.getAreaNo());
                }
            } else {
                log.warn("订单{}未找到匹配的快照信息，城市:{}, 区域:{}", orderEntity.getOuterOrderId(), orderCity, orderArea);
            }
        }

        log.info("匹配到需要切仓的订单数量:{}", matchedOrders.size());
        return matchedOrders;
    }

    /**
     * 更新表中的匹配结果
     */
    private void updateOrdersWithMatchResult(List<FenceChangeTaskOrderEntity> matchedOrderEntities) {
        if (CollectionUtils.isEmpty(matchedOrderEntities)) {
            return;
        }

        try {
            // 逐个更新订单的变更前后信息
            for (FenceChangeTaskOrderEntity orderEntity : matchedOrderEntities) {
                fenceChangeTaskDetailRepository.update(orderEntity);
            }
            log.info("更新订单匹配结果成功，订单数量:{}", matchedOrderEntities.size());
        } catch (Exception e) {
            log.error("更新订单匹配结果异常，异常信息:{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 进行切仓处理
     */
    private void processFenceChange(List<FenceChangeTaskOrderEntity> matchedOrderEntities,
                                   FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (CollectionUtils.isEmpty(matchedOrderEntities)) {
            log.info("自定义围栏切仓任务ID:{} 无需要切仓的订单，直接完结任务", waitOrderChangeHandleTask.getId());
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));
        } else {
            log.info("自定义围栏切仓任务ID:{} 需要切仓的订单数量:{}", waitOrderChangeHandleTask.getId(), matchedOrderEntities.size());

            // 更新任务状态为订单切换中
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.ORDER_CHANGE_ING));

            // TODO: 调用OFC进行切仓 - 需要OFC做支持
            // 这里可以调用OFC的切仓接口，传入需要切仓的订单列表
            log.info("开始调用OFC进行自定义围栏切仓处理，订单数量:{}", matchedOrderEntities.size());
        }
    }



    /**
     * 自定义区域信息
     */
    @Data
    private static class CustomAreaInfo {
        private String city;
        private String area;
        private String originalArea;
        private String customAreaName;
    }

    /**
     * 快照信息
     */
    @Data
    private static class SnapshotInfo {
        private Integer fenceId;
        private Integer storeNo;
        private Integer areaNo;
        private String city;
        private String area;
    }

}
