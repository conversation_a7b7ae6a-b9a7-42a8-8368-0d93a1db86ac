package net.summerfarm.wnc.application.service.changeTask;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义区域切订单服务
 * date: 2025/9/5 16:21<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForCustomFenceOrderChangeHandleService {

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    /**
     * 自定义区域切订单处理
     */
    public void customFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }

        // 1.根据切仓任务ID查询切仓变更批次
        List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities = wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(waitOrderChangeHandleTask.getId());

        // 2.变更前后对比，看城配仓是否有变化，运营区域是否有变化，有变化的需要把省市区给找出来构造省市区变更前的城配仓、运营区域、变更后的城配仓、运营区域
        Map<Integer, CustomAreaChangeInfo> areaChangeInfoMap = compareAndBuildCustomAreaChangeInfo(fenceChangeRecordsEntities);
        if (areaChangeInfoMap.isEmpty()) {
            log.info("自定义围栏切仓任务ID:{} 无区域变更，直接完结任务", waitOrderChangeHandleTask.getId());
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));
            return;
        }

        // 3.根据城市区域日期查询OFC单据
        List<FulfillmentOrderDTO> fulfillmentOrders = queryCustomFenceFulfillmentOrders(areaChangeInfoMap, waitOrderChangeHandleTask);

        // 4.根据OFC返回的数据用省市区查询有效的区域，然后根据有效区域ID和POI去查询es找出实际的区域ID
        List<FulfillmentOrderDTO> ordersWithActualAreaId = enrichOrdersWithActualAreaId(fulfillmentOrders, areaChangeInfoMap);

        // 5.过滤出需要切仓的订单数据
        List<FulfillmentOrderDTO> needChangeOrders = filterCustomFenceNeedChangeOrders(ordersWithActualAreaId, areaChangeInfoMap);

        // 6.保存到切仓订单明细表
        List<FenceChangeTaskOrderEntity> taskOrderEntities = saveCustomFenceTaskOrderDetails(needChangeOrders, waitOrderChangeHandleTask.getId());

        // 7.判断需要切仓的订单，如果需要切仓的订单为空，直接完结任务,否则调用OFC进行切仓并更新切仓任务状态
        if (CollectionUtils.isEmpty(taskOrderEntities)) {
            log.info("自定义围栏切仓任务ID:{} 无需要切仓的订单，直接完结任务", waitOrderChangeHandleTask.getId());
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));
        } else {
            log.info("自定义围栏切仓任务ID:{} 需要切仓的订单数量:{}", waitOrderChangeHandleTask.getId(), taskOrderEntities.size());
            // 更新任务状态为订单切换中
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.ORDER_CHANGE_ING));
            // TODO: 调用OFC进行切仓 - 需要OFC做支持
        }
    }

    /**
     * 比较变更前后的数据，构建自定义区域变更信息
     * 以adCodeMsgId为维度处理变更
     */
    private Map<Integer, CustomAreaChangeInfo> compareAndBuildCustomAreaChangeInfo(List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities) {
        Map<Integer, CustomAreaChangeInfo> areaChangeInfoMap = new HashMap<>();

        // 获取所有区域变更记录，按adCodeMsgId分组处理
        Map<Integer, List<WncFenceAreaChangeRecordsEntity>> adCodeAreaRecordsMap = new HashMap<>();
        Map<Integer, WncFenceChangeRecordsEntity> beforeRecordsMap = new HashMap<>();
        Map<Integer, WncFenceChangeRecordsEntity> afterRecordsMap = new HashMap<>();

        // 收集所有变更前后的记录
        for (WncFenceChangeRecordsEntity record : fenceChangeRecordsEntities) {
            if (Objects.equals(record.getFenceChangeStage(), WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue())) { // 变更前
                beforeRecordsMap.put(record.getFenceId(), record);
                // 收集变更前的区域记录
                if (!CollectionUtils.isEmpty(record.getAreaChangeRecords())) {
                    for (WncFenceAreaChangeRecordsEntity areaRecord : record.getAreaChangeRecords()) {
                        if (areaRecord.getAdCodeMsgId() != null) {
                            adCodeAreaRecordsMap.computeIfAbsent(areaRecord.getAdCodeMsgId(), k -> new ArrayList<>()).add(areaRecord);
                        }
                    }
                }
            } else if (Objects.equals(record.getFenceChangeStage(), WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue())) { // 变更后
                afterRecordsMap.put(record.getFenceId(), record);
            }
        }

        // 按adCodeMsgId维度分析变更
        for (Map.Entry<Integer, List<WncFenceAreaChangeRecordsEntity>> entry : adCodeAreaRecordsMap.entrySet()) {
            Integer adCodeMsgId = entry.getKey();
            List<WncFenceAreaChangeRecordsEntity> areaRecords = entry.getValue();

            // 获取该区域对应的围栏变更前后记录
            WncFenceAreaChangeRecordsEntity areaRecord = areaRecords.get(0); // 取第一个作为代表
            Integer fenceId = areaRecord.getFenceId();

            WncFenceChangeRecordsEntity beforeRecord = beforeRecordsMap.get(fenceId);
            WncFenceChangeRecordsEntity afterRecord = afterRecordsMap.get(fenceId);

            if (beforeRecord == null || afterRecord == null) {
                log.warn("未找到自定义围栏{}的完整变更记录，跳过adCodeMsgId:{}", fenceId, adCodeMsgId);
                continue;
            }

            // 检查城配仓或运营区域是否有变化
            boolean storeChanged = !Objects.equals(beforeRecord.getFenceStoreNo(), afterRecord.getFenceStoreNo());
            boolean areaNoChanged = !Objects.equals(beforeRecord.getFenceAreaNo(), afterRecord.getFenceAreaNo());

            if (storeChanged || areaNoChanged) {
                String city = areaRecord.getCity();
                String area = areaRecord.getArea();

                if (city == null || city.trim().isEmpty()) {
                    log.warn("自定义区域变更记录城市为空，跳过处理，adCodeMsgId:{}", adCodeMsgId);
                    continue;
                }

                // 自定义区域可能使用自定义区域名称
                if (area == null || area.trim().isEmpty()) {
                    area = areaRecord.getCustomAreaName();
                    if (area == null || area.trim().isEmpty()) {
                        log.warn("自定义区域变更记录区域和自定义区域名称都为空，跳过处理，adCodeMsgId:{}", adCodeMsgId);
                        continue;
                    }
                }

                CustomAreaChangeInfo changeInfo = new CustomAreaChangeInfo();
                changeInfo.setAdCodeMsgId(adCodeMsgId);
                changeInfo.setCity(city);
                changeInfo.setArea(area);
                changeInfo.setCustomAreaName(areaRecord.getCustomAreaName());
                changeInfo.setFenceId(fenceId);
                changeInfo.setOldStoreNo(beforeRecord.getFenceStoreNo());
                changeInfo.setNewStoreNo(afterRecord.getFenceStoreNo());
                changeInfo.setOldAreaNo(beforeRecord.getFenceAreaNo());
                changeInfo.setNewAreaNo(afterRecord.getFenceAreaNo());
                changeInfo.setStoreChanged(storeChanged);
                changeInfo.setAreaChanged(areaNoChanged);

                areaChangeInfoMap.put(adCodeMsgId, changeInfo);
                log.debug("构建自定义区域变更信息，adCodeMsgId:{}, 城市:{}, 区域:{}, 自定义区域名称:{}, 城配仓变更:{}->{}, 运营区域变更:{}->{}",
                        adCodeMsgId, city, area, areaRecord.getCustomAreaName(),
                        beforeRecord.getFenceStoreNo(), afterRecord.getFenceStoreNo(),
                        beforeRecord.getFenceAreaNo(), afterRecord.getFenceAreaNo());
            }
        }

        return areaChangeInfoMap;
    }

    /**
     * 查询自定义围栏T+1之后待履约的履约单数据
     */
    private List<FulfillmentOrderDTO> queryCustomFenceFulfillmentOrders(Map<Integer, CustomAreaChangeInfo> areaChangeInfoMap,
                                                                       FenceChangeTaskEntity waitOrderChangeHandleTask) {
        List<FulfillmentOrderDTO> allFulfillmentOrders = new ArrayList<>();

        // 按城市分组查询
        Map<String, List<String>> cityAreasMap = areaChangeInfoMap.values().stream()
                .collect(Collectors.groupingBy(
                        CustomAreaChangeInfo::getCity,
                        Collectors.mapping(info -> {
                            // 优先使用自定义区域名称，如果没有则使用标准区域名称
                            String areaName = info.getCustomAreaName();
                            return (areaName != null && !areaName.trim().isEmpty()) ? areaName : info.getArea();
                        }, Collectors.toList())
                ));

        for (Map.Entry<String, List<String>> entry : cityAreasMap.entrySet()) {
            String city = entry.getKey();
            List<String> areas = entry.getValue();

            // 获取该城市区域对应的城配仓编号（使用变更前的城配仓编号查询）
            Integer storeNo = areaChangeInfoMap.values().stream()
                    .filter(info -> city.equals(info.getCity()))
                    .map(CustomAreaChangeInfo::getOldStoreNo)
                    .findFirst().orElse(null);

            if (storeNo != null) {
                FulfillmentQueryInput queryInput = FulfillmentQueryInput.builder()
                        .city(city)
                        .areas(areas)
                        .storeNo(storeNo)
                        .deliveryDateBegin(waitOrderChangeHandleTask.getExeTimePlus2Date()) // T+2的时间点
                        .build();

                try {
                    List<FulfillmentOrderDTO> fulfillmentOrders = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(queryInput);
                    if (!CollectionUtils.isEmpty(fulfillmentOrders)) {
                        allFulfillmentOrders.addAll(fulfillmentOrders);
                    }
                } catch (Exception e) {
                    log.error("查询自定义围栏履约单数据异常，城市:{}, 区域:{}, 城配仓:{}, 异常信息:{}",
                            city, JSON.toJSONString(areas), storeNo, e.getMessage(), e);
                }
            }
        }

        log.info("自定义围栏切仓任务ID:{} 查询到待履约订单数量:{}", waitOrderChangeHandleTask.getId(), allFulfillmentOrders.size());
        return allFulfillmentOrders;
    }

    /**
     * 根据OFC返回的数据用省市区查询有效的区域，然后根据有效区域ID和POI去查询ES找出实际的区域ID
     */
    private List<FulfillmentOrderDTO> enrichOrdersWithActualAreaId(List<FulfillmentOrderDTO> fulfillmentOrders,
                                                                  Map<Integer, CustomAreaChangeInfo> areaChangeInfoMap) {
        if (CollectionUtils.isEmpty(fulfillmentOrders)) {
            return Collections.emptyList();
        }

        List<FulfillmentOrderDTO> enrichedOrders = new ArrayList<>();

        for (FulfillmentOrderDTO order : fulfillmentOrders) {
            try {
                // 根据订单的城市区域和POI查找匹配的adCodeMsgId
                Integer actualAdCodeMsgId = findActualAdCodeMsgIdByPoi(order, areaChangeInfoMap);

                if (actualAdCodeMsgId != null) {
                    order.setOldAdCodeMsgId(actualAdCodeMsgId);
                    order.setNewAdCodeMsgId(actualAdCodeMsgId);
                    enrichedOrders.add(order);
                    log.debug("为订单{}匹配到实际区域ID:{}", order.getOuterOrderId(), actualAdCodeMsgId);
                } else {
                    log.warn("订单{}未能匹配到实际区域ID，城市:{}, 区域:{}, POI:{}",
                            order.getOuterOrderId(), order.getCity(), order.getArea(), order.getPoi());
                }
            } catch (Exception e) {
                log.error("为订单{}查询实际区域ID异常，异常信息:{}", order.getOuterOrderId(), e.getMessage(), e);
            }
        }

        log.info("成功为{}个订单匹配到实际区域ID", enrichedOrders.size());
        return enrichedOrders;
    }

    /**
     * 根据订单的城市区域和POI查找匹配的adCodeMsgId
     * TODO: 这里需要调用ES服务根据POI坐标查询实际的区域ID
     */
    private Integer findActualAdCodeMsgIdByPoi(FulfillmentOrderDTO order, Map<Integer, CustomAreaChangeInfo> areaChangeInfoMap) {
        String orderCity = order.getCity();
        String orderArea = order.getArea();
        String orderPoi = order.getPoi();

        if (orderCity == null || orderArea == null) {
            return null;
        }

        // 先尝试直接匹配城市区域
        Integer directMatch = areaChangeInfoMap.values().stream()
                .filter(info -> orderCity.equals(info.getCity()) &&
                               (orderArea.equals(info.getArea()) || orderArea.equals(info.getCustomAreaName())))
                .map(CustomAreaChangeInfo::getAdCodeMsgId)
                .findFirst()
                .orElse(null);

        if (directMatch != null) {
            return directMatch;
        }

        // TODO: 如果直接匹配失败，需要调用ES服务根据POI坐标查询实际的区域ID
        // 这里需要集成ES查询服务，根据POI坐标查询对应的自定义区域
        log.debug("需要通过ES查询POI:{}对应的实际区域ID", orderPoi);

        return null;
    }

    /**
     * 过滤出自定义围栏城配仓有变更的订单数据
     */
    private List<FulfillmentOrderDTO> filterCustomFenceNeedChangeOrders(List<FulfillmentOrderDTO> fulfillmentOrders,
                                                                       Map<Integer, CustomAreaChangeInfo> areaChangeInfoMap) {
        if (CollectionUtils.isEmpty(fulfillmentOrders)) {
            return Collections.emptyList();
        }

        List<FulfillmentOrderDTO> needChangeOrders = new ArrayList<>();

        for (FulfillmentOrderDTO order : fulfillmentOrders) {
            // 检查订单的城市和区域是否为空
            String orderCity = order.getCity();
            String orderArea = order.getArea();

            if (orderCity == null || orderCity.trim().isEmpty()) {
                log.warn("自定义围栏履约订单城市为空，跳过处理，订单号:{}", order.getOuterOrderId());
                continue;
            }

            if (orderArea == null || orderArea.trim().isEmpty()) {
                log.warn("自定义围栏履约订单区域为空，跳过处理，订单号:{}", order.getOuterOrderId());
                continue;
            }

            // 根据订单的adCodeMsgId查找变更信息
            Integer adCodeMsgId = order.getOldAdCodeMsgId();
            if (adCodeMsgId != null) {
                CustomAreaChangeInfo changeInfo = areaChangeInfoMap.get(adCodeMsgId);
                if (changeInfo != null && changeInfo.isStoreChanged()) {
                    // 设置变更信息到订单中
                    order.setOldStoreNo(changeInfo.getOldStoreNo());
                    order.setNewStoreNo(changeInfo.getNewStoreNo());
                    order.setOldAreaNo(changeInfo.getOldAreaNo());
                    order.setNewAreaNo(changeInfo.getNewAreaNo());
                    order.setOldFenceId(changeInfo.getFenceId());
                    order.setNewFenceId(changeInfo.getFenceId());

                    needChangeOrders.add(order);
                }
            }
        }

        log.info("过滤出自定义围栏需要切仓的订单数量:{}", needChangeOrders.size());
        return needChangeOrders;
    }

    /**
     * 保存自定义围栏切仓订单明细
     */
    private List<FenceChangeTaskOrderEntity> saveCustomFenceTaskOrderDetails(List<FulfillmentOrderDTO> needChangeOrders, Long taskId) {
        if (CollectionUtils.isEmpty(needChangeOrders)) {
            return Collections.emptyList();
        }

        List<FenceChangeTaskOrderEntity> taskOrderEntities = new ArrayList<>();

        for (FulfillmentOrderDTO order : needChangeOrders) {
            try {
                FenceChangeTaskOrderEntity taskOrderEntity = FenceChangeTaskOrderConverter.dto2Entity(order);
                taskOrderEntity.create(taskId);
                taskOrderEntities.add(taskOrderEntity);
            } catch (Exception e) {
                log.error("转换自定义围栏订单数据异常，订单号:{}, 异常信息:{}", order.getOuterOrderId(), e.getMessage(), e);
            }
        }

        if (!CollectionUtils.isEmpty(taskOrderEntities)) {
            try {
                // 去重处理，避免重复插入
                Set<FenceChangeTaskOrderEntity> uniqueEntities = new LinkedHashSet<>(taskOrderEntities);
                fenceChangeTaskDetailRepository.saveBatch(uniqueEntities);
                log.info("保存自定义围栏切仓订单明细成功，任务ID:{}, 订单数量:{}", taskId, uniqueEntities.size());
                return new ArrayList<>(uniqueEntities);
            } catch (DuplicateKeyException e) {
                log.warn("保存自定义围栏切仓订单明细存在重复数据，任务ID:{}, 异常信息:{}", taskId, e.getMessage());
                return taskOrderEntities;
            } catch (Exception e) {
                log.error("保存自定义围栏切仓订单明细异常，任务ID:{}, 异常信息:{}", taskId, e.getMessage(), e);
                throw e;
            }
        }

        return taskOrderEntities;
    }

    /**
     * 自定义区域变更信息
     */
    @Data
    private static class CustomAreaChangeInfo {
        private Integer adCodeMsgId;
        private Integer fenceId;
        private String city;
        private String area;
        private String customAreaName;
        private Integer oldStoreNo;
        private Integer newStoreNo;
        private Integer oldAreaNo;
        private Integer newAreaNo;
        private boolean storeChanged;
        private boolean areaChanged;
    }

}
