package net.summerfarm.wnc.domain.fence.service;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.AdCodeMsgEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.domain.fence.AdCodeMsgRepository;
import net.summerfarm.wnc.domain.fence.AreaRepository;
import net.summerfarm.wnc.domain.fence.FenceDomainService;
import net.summerfarm.wnc.domain.fence.entity.*;
import net.summerfarm.wnc.domain.fence.param.command.AddCustomFenceChangeRecordCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.FenceChannelBusinessWhiteConfigCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsMappingRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 *
 * @Title: 围栏区域变更记录领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Slf4j
@Service
public class WncFenceAreaChangeRecordsCommandDomainService {


    @Autowired
    private WncFenceAreaChangeRecordsCommandRepository wncFenceAreaChangeRecordsCommandRepository;
    @Autowired
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;
    @Autowired
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Autowired
    private WncFenceChangeRecordsCommandRepository wncFenceChangeRecordsCommandRepository;
    @Autowired
    private AdCodeMsgRepository adCodeMsgRepository;
    @Autowired
    private FenceDomainService fenceDomainService;
    @Autowired
    private WarehouseLogisticsMappingRepository warehouseLogisticsMappingRepository;
    @Autowired
    private WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
    @Autowired
    private WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    @Autowired
    private AreaRepository areaRepository;



    public WncFenceAreaChangeRecordsEntity insert(WncFenceAreaChangeRecordsCommandParam param) {
        return wncFenceAreaChangeRecordsCommandRepository.insertSelective(param);
    }


    public int update(WncFenceAreaChangeRecordsCommandParam param) {
        return wncFenceAreaChangeRecordsCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return wncFenceAreaChangeRecordsCommandRepository.remove(id);
    }

    /**
     * 更新围栏区域变更记录的围栏ID和ACM区域ID
     * @param params 围栏区域变更记录
     */
    public void updateAdCodeIdAndFenceId(List<WncFenceAreaChangeRecordsCommandParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        wncFenceAreaChangeRecordsCommandRepository.batchUpdate(params);
    }

    /**
     * 新增自定义围栏变更记录
     * @param param 新增参数
     * @return 围栏变更记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addCustomFenceChangeRecord(AddCustomFenceChangeRecordCommandParam param) {
        // 根据cityAreaChangeWarehouseRecordId查询省市区
        WncCityAreaChangeWarehouseRecordsEntity cityAreaRecord = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(param.getCityAreaChangeWarehouseRecordId());
        if (cityAreaRecord == null) {
            throw new BizException("城市区域变更记录不存在");
        }

        // 新增变更前围栏数据
        addBeforeChangeRecord(cityAreaRecord.getProvince(), cityAreaRecord.getCity(), cityAreaRecord.getArea(), cityAreaRecord.getChangeBatchNo());

        // 新增变更后围栏数据
        Long fenceChangeRecordsId = addAfterChangeRecord(cityAreaRecord.getChangeBatchNo(), param);

        return fenceChangeRecordsId;
    }

    /**
     * 新增变更前的围栏记录
     */
    private void addBeforeChangeRecord(String province, String city, String area, String changeBatchNo) {
        // 根据省市区查询ad_code_msg
        AdCodeMsgQuery adCodeMsgQuery = AdCodeMsgQuery.builder()
                .province(province)
                .city(city)
                .area(area)
                .status(AdCodeMsgEnums.Status.VALID.getValue())
                .build();
        List<AdCodeMsgEntity> adCodeMsgEntities = adCodeMsgRepository.queryList(adCodeMsgQuery);

        if (CollectionUtils.isEmpty(adCodeMsgEntities)) {
            throw new BizException("未找到对应的区域信息");
        }

        // 获取所有涉及的围栏ID
        List<Integer> fenceIds = adCodeMsgEntities.stream()
                .map(AdCodeMsgEntity::getFenceId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<FenceEntity> fenceEntities = fenceDomainService.queryFenceAllDetailList(fenceIds);
        Map<Integer, FenceEntity> fenceEntityMap = fenceEntities.stream().collect(Collectors.toMap(FenceEntity::getId, Function.identity()));
        List<Integer> storeNoList = fenceEntities.stream().map(FenceEntity::getStoreNo).collect(Collectors.toList());
        // 查询映射
        Map<Integer, List<Integer>> storeWarehouseMap = warehouseLogisticsMappingRepository.queryLogisticsMapping(storeNoList);
        // 城配仓名称
        Map<Integer, String> storeNoToNameMap = warehouseLogisticsCenterRepository.queryStoreNoToNameMapByStoreNos(storeNoList);
        // 运营区域名称
        List<Integer> areaNoList = fenceEntities.stream().map(FenceEntity::getAreaNo).collect(Collectors.toList());
        Map<Integer, String> areaNoToNameMap = areaRepository.queryAreaNoToNameMapByAreaNos(areaNoList);

        // 为每个围栏创建变更前记录
        for (Integer fenceId : fenceIds) {
            // 围栏变更记录组装
            WncFenceChangeRecordsCommandParam fenceChangeParam = new WncFenceChangeRecordsCommandParam();
            fenceChangeParam.setFenceId(fenceId);
            fenceChangeParam.setChangeBatchNo(changeBatchNo);
            fenceChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue());
            // 现有围栏信息
            FenceEntity fenceEntity = fenceEntityMap.get(fenceId);
            if (null != fenceEntity) {
                fenceChangeParam.setFenceName(fenceEntity.getFenceName());
                fenceChangeParam.setFenceStoreNo(fenceEntity.getStoreNo());
                fenceChangeParam.setFenceStoreName(storeNoToNameMap.get(fenceEntity.getStoreNo()));
                fenceChangeParam.setFenceAreaNo(fenceEntity.getAreaNo());
                fenceChangeParam.setFenceAreaName(areaNoToNameMap.get(fenceEntity.getAreaNo()));
                fenceChangeParam.setFenceMsg(JSON.toJSONString(fenceEntity));
                List<Integer> warehouseNoList = storeWarehouseMap.get(fenceEntity.getStoreNo());
                if (CollectionUtils.isEmpty(warehouseNoList)) {
                    throw new BizException("未找到城配仓对应的仓库映射");
                }
                fenceChangeParam.setFenceWarehouseNos(Joiner.on(",").join(warehouseNoList));
                List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryListByWarehouseNos(warehouseNoList);
                if (CollectionUtils.isEmpty(warehouseStorageEntityList)) {
                    throw new BizException("未找到城配仓对应的仓库信息");
                }
                fenceChangeParam.setFenceWarehouseNames(Joiner.on(",").join(warehouseStorageEntityList.stream().map(WarehouseStorageEntity::getWarehouseName).collect(Collectors.toList())));
            }
            // 新增围栏变更记录
            WncFenceChangeRecordsEntity fenceChangeRecord = wncFenceChangeRecordsCommandRepository.insertSelective(fenceChangeParam);
            if (null == fenceChangeRecord || null == fenceChangeRecord.getId()) {
                throw new BizException("新增变更前围栏变更记录失败");
            }

            // 为该围栏下的所有区域创建变更前记录
            List<AdCodeMsgEntity> fenceAdCodeMsgEntities = adCodeMsgEntities.stream()
                    .filter(entity -> Objects.equals(entity.getFenceId(), fenceId))
                    .collect(Collectors.toList());
            for (AdCodeMsgEntity adCodeMsgEntity : fenceAdCodeMsgEntities) {
                WncFenceAreaChangeRecordsCommandParam areaChangeParam = new WncFenceAreaChangeRecordsCommandParam();
                areaChangeParam.setFenceChangeId(fenceChangeRecord.getId());
                areaChangeParam.setFenceId(fenceId);
                areaChangeParam.setCity(adCodeMsgEntity.getCity());
                areaChangeParam.setArea(adCodeMsgEntity.getArea());
                areaChangeParam.setAdCodeMsgId(adCodeMsgEntity.getId());
                areaChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue());
                areaChangeParam.setAreaDrawType(adCodeMsgEntity.getAreaDrawType());
                // 新增围栏区域变更记录
                wncFenceAreaChangeRecordsCommandRepository.insertSelective(areaChangeParam);
            }
        }
    }

    /**
     * 新增变更后的围栏记录
     */
    private Long addAfterChangeRecord(String changeBatchNo, AddCustomFenceChangeRecordCommandParam param) {
        // 查询映射
        List<Integer> warehouseNoList = warehouseLogisticsMappingRepository.queryWarehouseNosByStoreNo(param.getStoreNo());
        if (CollectionUtils.isEmpty(warehouseNoList)) {
            throw new BizException("未找到城配仓对应的仓库映射");
        }
        List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryListByWarehouseNos(warehouseNoList);
        if (CollectionUtils.isEmpty(warehouseStorageEntityList)) {
            throw new BizException("未找到城配仓对应的仓库信息");
        }

        // 变更后的围栏记录实体
        WncFenceChangeRecordsCommandParam fenceChangeParam = new WncFenceChangeRecordsCommandParam();
        fenceChangeParam.setChangeBatchNo(changeBatchNo);
        fenceChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
        fenceChangeParam.setFenceStoreNo(param.getStoreNo());
        fenceChangeParam.setFenceStoreName(param.getStoreName());
        fenceChangeParam.setFenceAreaNo(param.getAreaNo());
        fenceChangeParam.setFenceAreaName(param.getAreaName());
        fenceChangeParam.setFenceWarehouseNos(Joiner.on(",").join(warehouseNoList));
        fenceChangeParam.setFenceWarehouseNames(Joiner.on(",").join(warehouseStorageEntityList.stream().map(WarehouseStorageEntity::getWarehouseName).collect(Collectors.toList())));
        // 围栏实体
        FenceEntity fenceEntity = new FenceEntity();
        fenceEntity.setFenceName(param.getFenceName());
        fenceEntity.setStoreNo(param.getStoreNo());
        fenceEntity.setStoreName(param.getStoreName());
        fenceEntity.setAreaNo(param.getAreaNo());
        fenceEntity.setAreaName(param.getAreaName());
        fenceEntity.setType(net.summerfarm.wnc.common.enums.FenceEnums.Type.CUSTOM);
        fenceEntity.setStatus(FenceEnums.Status.VALID.getValue().equals(param.getFenceStatus()) ? FenceEnums.Status.VALID : FenceEnums.Status.STOP);
        // 配送周期
        FenceDeliveryEntity fenceDeliveryEntity = new FenceDeliveryEntity();
        fenceDeliveryEntity.setDeliveryFrequent(param.getFenceDeliveryCommandParam().getDeliveryFrequent());
        fenceDeliveryEntity.setNextDeliveryDate(param.getFenceDeliveryCommandParam().getNextDeliveryDate());
        fenceDeliveryEntity.setBeginCalculateDate(param.getFenceDeliveryCommandParam().getBeginCalculateDate());
        fenceDeliveryEntity.setFrequentMethod(param.getFenceDeliveryCommandParam().getFrequentMethod());
        fenceDeliveryEntity.setDeliveryFrequentInterval(param.getFenceDeliveryCommandParam().getDeliveryFrequentInterval());
        fenceEntity.setFenceDeliveryEntity(fenceDeliveryEntity);
        // 下单渠道白名单配置
        List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntities = new ArrayList<>();
        for (FenceChannelBusinessWhiteConfigCommandParam fenceChannelBusinessWhiteConfigCommandParam : param.getFenceChannelBusinessWhiteConfigCommandParams()) {
            FenceChannelBusinessWhiteConfigEntity fenceChannelBusinessWhiteConfigEntity = new FenceChannelBusinessWhiteConfigEntity();
            fenceChannelBusinessWhiteConfigEntity.setOrderChannelType(fenceChannelBusinessWhiteConfigCommandParam.getOrderChannelType());
            fenceChannelBusinessWhiteConfigEntity.setTenantId(fenceChannelBusinessWhiteConfigCommandParam.getTenantId());
            fenceChannelBusinessWhiteConfigEntity.setScopeChannelBusinessId(fenceChannelBusinessWhiteConfigCommandParam.getScopeChannelBusinessId());
            fenceChannelBusinessWhiteConfigEntity.setScopeChannelBusinessName(fenceChannelBusinessWhiteConfigCommandParam.getScopeChannelBusinessName());
            fenceChannelBusinessWhiteConfigEntities.add(fenceChannelBusinessWhiteConfigEntity);
        }
        fenceEntity.setFenceChannelBusinessWhiteConfigEntities(fenceChannelBusinessWhiteConfigEntities);
        fenceChangeParam.setFenceMsg(JSON.toJSONString(fenceEntity));
        // 新增变更后围栏变更记录
        WncFenceChangeRecordsEntity afterChangeRecord = wncFenceChangeRecordsCommandRepository.insertSelective(fenceChangeParam);
        if (null == afterChangeRecord || null == afterChangeRecord.getId()) {
            throw new BizException("新增变更后围栏变更记录失败");
        }

        return afterChangeRecord.getId();
    }

    /**
     * 新增自定义围栏区域变更记录
     * @param fenceChangeRecordsId 围栏变更记录ID
     * @param customAreaName 自定义区域名称
     * @param geoShape 坐标POI图形
     * @param city 市
     * @param area 区
     * @param areaDrawType 区域绘制类型
     * @return 围栏区域变更记录ID
     */
    public Long addCustomFenceAreaChangeRecord(Long fenceChangeRecordsId, String customAreaName, String geoShape,
                                               String city, String area, Integer areaDrawType) {
        // 新增自定义围栏区域变更记录
        WncFenceAreaChangeRecordsCommandParam areaChangeParam = new WncFenceAreaChangeRecordsCommandParam();
        areaChangeParam.setFenceChangeId(fenceChangeRecordsId);
        areaChangeParam.setCustomAreaName(customAreaName);
        areaChangeParam.setGeoShape(geoShape);
        areaChangeParam.setCity(city);
        areaChangeParam.setArea(area);
        areaChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
        areaChangeParam.setAreaDrawType(areaDrawType);

        WncFenceAreaChangeRecordsEntity savedEntity = wncFenceAreaChangeRecordsCommandRepository.insertSelective(areaChangeParam);
        return savedEntity.getId();
    }
}
